<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>降采样测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .canvas-container {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        .canvas-wrapper {
            text-align: center;
            flex: 1;
            min-width: 300px;
        }
        .canvas-wrapper h3 {
            margin-bottom: 10px;
            color: #555;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 5px;
            max-width: 100%;
            height: auto;
        }
        .info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #1976D2;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>降采样功能测试</h1>
        
        <div class="info">
            <strong>测试目的：</strong>验证输入图像在推理开始时就被降采样到固定尺寸（224x224），确保所有中间节点使用统一的固定尺寸。
        </div>

        <div class="controls">
            <button onclick="loadLargeImage()">加载大图像 (512x512)</button>
            <button onclick="loadSmallImage()">加载小图像 (200x200)</button>
            <button onclick="createTestPattern()">创建测试图案</button>
        </div>

        <div class="canvas-container">
            <div class="canvas-wrapper">
                <h3>原始输入</h3>
                <canvas id="original-canvas" width="400" height="400"></canvas>
                <p id="original-info"></p>
            </div>
            <div class="canvas-wrapper">
                <h3>降采样后 (224x224)</h3>
                <canvas id="resized-canvas" width="224" height="224"></canvas>
                <p id="resized-info"></p>
            </div>
        </div>

        <div class="log">
            <h4>日志输出：</h4>
            <div id="log-output"></div>
        </div>
    </div>

    <script type="module">
        import { WebGLONNXInference } from './dist/webgl-onnx-inference.es.js';

        let inference = null;
        const originalCanvas = document.getElementById('original-canvas');
        const resizedCanvas = document.getElementById('resized-canvas');
        const originalInfo = document.getElementById('original-info');
        const resizedInfo = document.getElementById('resized-info');
        const logOutput = document.getElementById('log-output');

        function log(message) {
            console.log(message);
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        async function initializeInference() {
            try {
                log('正在初始化推理引擎...');
                
                // 创建离屏canvas用于WebGL
                const canvas = document.createElement('canvas');
                canvas.width = 224;
                canvas.height = 224;
                
                inference = new WebGLONNXInference();
                const success = await inference.initialize({
                    canvas: canvas,
                    debugMode: true
                });
                
                if (success) {
                    log('✅ 推理引擎初始化成功');
                } else {
                    log('❌ 推理引擎初始化失败');
                }
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`);
            }
        }

        async function testResizing(imageElement, description) {
            if (!inference) {
                await initializeInference();
                if (!inference) return;
            }

            try {
                log(`🔄 开始测试: ${description}`);
                
                // 显示原始图像
                const originalCtx = originalCanvas.getContext('2d');
                originalCanvas.width = imageElement.width;
                originalCanvas.height = imageElement.height;
                originalCtx.drawImage(imageElement, 0, 0);
                originalInfo.textContent = `尺寸: ${imageElement.width}x${imageElement.height}`;
                
                // 设置输入到推理引擎（这会触发降采样）
                log(`设置输入图像，原始尺寸: ${imageElement.width}x${imageElement.height}`);
                inference.setInputFromImage(imageElement);
                
                // 创建一个简单的测试模型
                await createSimpleTestModel();
                
                // 执行推理以触发降采样
                log('执行推理以验证降采样...');
                const result = await inference.inference();
                
                // 获取输出纹理并渲染到降采样canvas
                const outputTexture = inference.getOutputTexture();
                if (outputTexture) {
                    inference.renderToCanvas(resizedCanvas);
                    resizedInfo.textContent = `尺寸: 224x224 (固定)`;
                    log('✅ 降采样成功完成');
                }
                
                log(`推理完成，耗时: ${result.inferenceTime.toFixed(2)}ms`);
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                console.error(error);
            }
        }

        async function createSimpleTestModel() {
            try {
                // 这里我们创建一个极简的ONNX模型用于测试
                // 实际应用中，您会加载真实的ONNX模型
                log('创建测试模型...');
                
                // 由于我们主要测试降采样功能，使用简单的恒等操作
                // 在实际应用中，这里应该加载真实的ONNX模型文件
                log('⚠️ 注意：此测试使用简化的模型，实际应用请加载真实ONNX模型');
                
            } catch (error) {
                log(`创建测试模型失败: ${error.message}`);
            }
        }

        window.loadLargeImage = async function() {
            const img = new Image();
            img.onload = async function() {
                await testResizing(img, '大图像测试 (512x512)');
            };
            img.onerror = function() {
                log('❌ 加载大图像失败，使用生成的大图像');
                createLargeImage();
            };
            // 使用在线图片或生成测试图案
            img.src = 'https://picsum.photos/512/512?random=1';
        };

        window.loadSmallImage = async function() {
            const img = new Image();
            img.onload = async function() {
                await testResizing(img, '小图像测试 (200x200)');
            };
            img.onerror = function() {
                log('❌ 加载小图像失败，使用生成的小图像');
                createSmallImage();
            };
            img.src = 'https://picsum.photos/200/200?random=2';
        };

        window.createTestPattern = function() {
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 400;
            const ctx = canvas.getContext('2d');
            
            // 创建彩色渐变图案
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.25, '#4ecdc4');
            gradient.addColorStop(0.5, '#45b7d1');
            gradient.addColorStop(0.75, '#96ceb4');
            gradient.addColorStop(1, '#ffeaa7');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 添加一些几何图案
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(canvas.width/2, canvas.height/2, 100, 0, 2 * Math.PI);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.rect(50, 50, canvas.width-100, canvas.height-100);
            ctx.stroke();
            
            // 添加文本
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试图案', canvas.width/2, canvas.height/2);
            
            // 转换为图像
            const img = new Image();
            img.onload = async function() {
                await testResizing(img, '测试图案 (400x400)');
            };
            img.src = canvas.toDataURL();
        };

        function createLargeImage() {
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 512;
            const ctx = canvas.getContext('2d');
            
            // 创建复杂的图案
            for (let i = 0; i < 64; i++) {
                for (let j = 0; j < 64; j++) {
                    ctx.fillStyle = `hsl(${(i + j) * 6}, 70%, 60%)`;
                    ctx.fillRect(i * 8, j * 8, 8, 8);
                }
            }
            
            const img = new Image();
            img.onload = async function() {
                await testResizing(img, '生成的大图像 (512x512)');
            };
            img.src = canvas.toDataURL();
        }

        function createSmallImage() {
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // 创建简单的图案
            const gradient = ctx.createRadialGradient(100, 100, 0, 100, 100, 100);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 200, 200);
            
            const img = new Image();
            img.onload = async function() {
                await testResizing(img, '生成的小图像 (200x200)');
            };
            img.src = canvas.toDataURL();
        }

        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', async () => {
            log('🚀 降采样功能测试页面加载完成');
            await initializeInference();
        });
    </script>
</body>
</html>