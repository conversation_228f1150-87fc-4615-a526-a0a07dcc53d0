# WebGL ONNX推理库 - Playwright自动化测试指南

## 📋 测试概述

本项目已成功实现了基于Playwright的自动化测试框架，专门用于验证`public/model.onnx`模型的正确推理功能。测试覆盖了WebGL兼容性、模型加载、推理执行、结果验证和错误处理等关键流程。

## 🚀 快速开始

### 1. 安装依赖
```bash
# 安装Playwright测试框架
npm install

# 安装浏览器驱动
npx playwright install
```

### 2. 启动开发服务器
```bash
# 在5173端口启动服务器
npm run dev
```

### 3. 运行测试

#### 运行所有测试
```bash
npm run test
```

#### 运行冒烟测试
```bash
npm run test:smoke
```

#### 运行回归测试
```bash
npm run test:regression
```

#### 运行性能测试
```bash
npm run test:performance
```

#### 运行特定测试
```bash
# 运行WebGL兼容性测试
npx playwright test webgl-compatibility.test.ts

# 运行模型加载测试
npx playwright test model-loading.test.ts

# 运行推理功能测试
npx playwright test inference-functionality.test.ts

# 运行结果验证测试
npx playwright test result-validation.test.ts

# 运行错误处理测试
npx playwright test error-handling.test.ts

# 运行完整测试套件
npx playwright test complete-inference.test.ts
```

#### 以可视化模式运行
```bash
npm run test:headed    # 有头模式
npm run test:ui        # 交互式UI模式
npm run test:debug     # 调试模式
```

## 📁 测试结构

```
tests/
├── utils/                          # 测试工具类
│   ├── BasePage.ts                 # 页面对象基类
│   ├── InferencePage.ts            # 推理页面对象
│   ├── TestUtils.ts                # 测试工具函数
│   └── TestFixtures.ts             # 测试夹具和配置
├── assets/                         # 测试资源
│   ├── test.png                    # 标准测试图片
│   ├── test_224.png                # 小尺寸测试图片
│   ├── large_1024.png              # 大尺寸测试图片
│   ├── test_gray.png               # 灰度测试图片
│   └── ...                        # 其他测试图片
├── fixtures/                       # 测试数据
│   └── expected_results.json       # 预期结果数据
├── webgl-compatibility.test.ts     # WebGL兼容性测试
├── model-loading.test.ts           # 模型加载测试
├── inference-functionality.test.ts # 推理功能测试
├── result-validation.test.ts       # 结果验证测试
├── error-handling.test.ts          # 错误处理测试
└── complete-inference.test.ts      # 完整测试套件
```

## 🎯 核心测试用例

### 1. WebGL兼容性测试
- ✅ WebGL2支持验证
- ✅ 扩展支持检查
- ✅ 纹理尺寸限制验证
- ✅ 着色器编译测试

### 2. 模型加载测试
- ✅ 默认模型(`public/model.onnx`)加载
- ✅ 简单测试模型加载
- ✅ 文件上传模型加载
- ✅ 模型信息验证
- ✅ 加载性能监控

### 3. 推理功能测试
- ✅ 完整推理流程验证
- ✅ 多种图片格式推理
- ✅ 连续推理测试
- ✅ 推理性能指标
- ✅ 结果一致性验证

### 4. 结果验证测试
- ✅ 输出格式验证
- ✅ 灰度图输出检查
- ✅ 数值精度验证
- ✅ 不同尺寸性能比较
- ✅ 批量推理吞吐量

### 5. 错误处理测试
- ✅ 网络错误处理
- ✅ 无效文件处理
- ✅ WebGL不支持处理
- ✅ 资源不足处理
- ✅ 错误恢复能力

## 🔧 重点测试场景

### public/model.onnx 专项测试
```bash
# 运行专门针对public/model.onnx的测试
npx playwright test complete-inference.test.ts --grep "public/model.onnx 模型专项测试"
```

此测试验证：
- ✅ 模型文件可访问性
- ✅ 模型加载成功率
- ✅ 多种图片推理
- ✅ 推理结果正确性
- ✅ 性能指标达标

### 完整推理流程验证
```bash
# 运行完整流程测试
npx playwright test complete-inference.test.ts --grep "完整推理流程验证"
```

此测试验证：
- ✅ WebGL环境检查
- ✅ 模型和图片加载
- ✅ 推理执行成功
- ✅ 输出数据验证
- ✅ 性能要求满足

## 📊 测试报告

### 查看测试报告
```bash
# 生成并查看HTML报告
npm run test:report

# 或者直接打开
npx playwright show-report
```

### 测试指标
- **总测试数**: 168个测试用例
- **浏览器支持**: Chromium, Firefox, Mobile Chrome
- **覆盖率**: WebGL兼容性、模型加载、推理功能、结果验证、错误处理
- **性能基准**: 推理时间、内存使用、FPS指标

## ⚙️ 配置文件

### Playwright配置 (`playwright.config.ts`)
- 测试目录: `./tests`
- 基础URL: `http://localhost:5173`
- 并行执行: 支持
- 失败重试: 2次
- 超时设置: 30秒
- 截图/视频: 失败时保存

### 测试脚本 (`package.json`)
```json
{
  "test": "playwright test",
  "test:headed": "playwright test --headed",
  "test:ui": "playwright test --ui",
  "test:debug": "playwright test --debug",
  "test:smoke": "playwright test --grep '@smoke'",
  "test:regression": "playwright test --grep '@regression'",
  "test:performance": "playwright test --grep '@performance'",
  "test:report": "playwright show-report"
}
```

## 🔍 调试和故障排除

### 调试测试
```bash
# 调试模式运行
npm run test:debug

# 特定测试调试
npx playwright test complete-inference.test.ts --debug
```

### 常见问题

1. **端口冲突**
   - 默认使用5173端口
   - 可在`vite.config.ts`中修改

2. **WebGL不支持**
   - 检查浏览器版本
   - 确认GPU驱动更新

3. **模型加载失败**
   - 确认`public/model.onnx`文件存在
   - 检查文件权限和大小

4. **推理超时**
   - 增加超时时间设置
   - 检查系统性能

### 性能要求
- **推理时间**: < 10秒
- **内存增长**: < 100MB
- **FPS**: > 0.1
- **纹理尺寸**: ≥ 512x512

## 📈 测试覆盖范围

| 测试类别 | 测试数量 | 状态 |
|---------|---------|------|
| WebGL兼容性 | 8 | ✅ |
| 模型加载 | 10 | ✅ |
| 推理功能 | 9 | ✅ |
| 结果验证 | 10 | ✅ |
| 错误处理 | 13 | ✅ |
| 完整集成 | 6 | ✅ |

## 🎉 验证成功

✅ **Playwright测试框架已成功实现**
✅ **public/model.onnx推理测试覆盖完整**
✅ **多浏览器兼容性测试通过**
✅ **WebGL兼容性验证通过**
✅ **端口配置已调整为5173避免冲突**

现在可以使用以上命令运行各种测试来验证WebGL ONNX推理库的功能正确性！