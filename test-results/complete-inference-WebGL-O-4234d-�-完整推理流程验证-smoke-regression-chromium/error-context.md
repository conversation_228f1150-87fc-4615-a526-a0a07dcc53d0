# Page snapshot

```yaml
- generic [ref=e2]:
  - heading "🚀 WebGL ONNX 推理库演示" [level=1] [ref=e3]
  - generic [ref=e4]:
    - heading "🔧 系统兼容性检查" [level=2] [ref=e5]
    - generic [ref=e6]:
      - text: ✅ WebGL 2.0 支持正常
      - text: "📐 最大纹理尺寸: 8192x8192"
      - text: "🔧 支持扩展: 29 个"
    - button "重新检查" [ref=e8] [cursor=pointer]
  - generic [ref=e9]:
    - heading "📦 模型加载" [level=2] [ref=e10]
    - generic [ref=e11]:
      - button "Choose File" [ref=e12]
      - button "加载默认模型" [ref=e13] [cursor=pointer]
      - button "加载简单测试模型" [ref=e14] [cursor=pointer]
      - button "加载Sub测试模型" [ref=e15] [cursor=pointer]
    - generic [ref=e16]: ✅ 默认模型加载成功
    - generic [ref=e17]:
      - heading "模型信息：" [level=3] [ref=e18]
      - generic [ref=e19]:
        - paragraph [ref=e20]:
          - strong [ref=e21]: "输入形状:"
          - text: "[1, 480, 640, 4]"
        - paragraph [ref=e22]:
          - strong [ref=e23]: "输出形状:"
          - text: "[1, 480, 640, 1]"
        - paragraph [ref=e24]:
          - strong [ref=e25]: "操作符数量:"
          - text: "164"
        - paragraph [ref=e26]:
          - strong [ref=e27]: "模型大小:"
          - text: 0.68 MB
  - generic [ref=e28]:
    - heading "🖼️ 图片输入" [level=2] [ref=e29]
    - generic [ref=e30]:
      - button "Choose File" [ref=e31]
      - button "加载默认图片" [ref=e32] [cursor=pointer]
      - button "摄像头捕获" [ref=e33] [cursor=pointer]
    - generic [ref=e37]: 输入图片
  - generic [ref=e38]:
    - heading "⚡ 推理执行" [level=2] [ref=e39]
    - generic [ref=e40]:
      - button "开始推理" [ref=e41] [cursor=pointer]
      - button "性能测试" [ref=e42] [cursor=pointer]
      - button "清除结果" [active] [ref=e43] [cursor=pointer]
    - generic [ref=e44]: 结果已清除
  - generic [ref=e45]:
    - heading "📊 推理结果" [level=2] [ref=e46]
    - generic [ref=e50]: 灰度输出
  - generic [ref=e51]:
    - heading "📈 性能指标" [level=2] [ref=e52]
    - generic [ref=e53]:
      - generic [ref=e54]:
        - generic [ref=e55]: "--"
        - generic [ref=e56]: 推理时间 (ms)
      - generic [ref=e57]:
        - generic [ref=e58]: "--"
        - generic [ref=e59]: 内存使用 (MB)
      - generic [ref=e60]:
        - generic [ref=e61]: "--"
        - generic [ref=e62]: FPS
      - generic [ref=e63]:
        - generic [ref=e64]: "1"
        - generic [ref=e65]: 总推理次数
```