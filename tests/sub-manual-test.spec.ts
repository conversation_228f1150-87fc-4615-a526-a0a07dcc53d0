import { test, expect } from '@playwright/test';

test('Sub操作符源码级别测试', async ({ page }) => {
  console.log('🧪 开始源码级别Sub操作符测试');
  
  // 访问手动测试页面
  await page.goto('http://localhost:8000/test_sub_manual.html');
  await page.waitForLoadState('networkidle');
  
  // 监听控制台消息
  let testResults = [];
  page.on('console', msg => {
    console.log(`浏览器控制台: ${msg.text()}`);
    testResults.push(msg.text());
  });
  
  // 点击测试按钮
  await page.click('button:has-text("开始测试Sub操作符")');
  
  // 等待测试完成
  await page.waitForFunction(() => {
    const status = document.querySelector('#status');
    return status && (
      status.textContent?.includes('测试全部通过') ||
      status.textContent?.includes('测试失败')
    );
  }, { timeout: 30000 });
  
  // 获取最终状态
  const finalStatus = await page.textContent('#status');
  console.log(`最终测试状态: ${finalStatus}`);
  
  // 获取详细日志
  const logContent = await page.textContent('#log');
  console.log('测试日志:');
  console.log(logContent);
  
  // 验证测试结果
  if (finalStatus?.includes('测试全部通过')) {
    console.log('🎉 Sub操作符源码级别测试成功！');
  } else {
    throw new Error(`Sub操作符测试失败: ${finalStatus}`);
  }
});