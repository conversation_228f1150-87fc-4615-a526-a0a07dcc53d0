import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';
import path from 'path';

/**
 * ONNX模型推理功能测试套件
 * 验证模型推理的完整流程和功能正确性
 */
test.describe('ONNX模型推理功能测试', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('完整推理流程测试', async () => {
    // 1. 加载模型
    await inferencePage.loadDefaultModel();
    expect(await inferencePage.isModelLoaded(), '模型应该加载成功').toBe(true);
    
    // 2. 加载图片
    await inferencePage.loadDefaultImage();
    expect(await inferencePage.isImageLoaded(), '图片应该加载成功').toBe(true);
    
    // 3. 等待推理准备就绪
    await inferencePage.waitForInferenceReady();
    expect(await inferencePage.isInferenceButtonEnabled(), '推理按钮应该可用').toBe(true);
    
    // 4. 执行推理
    await inferencePage.runInference();
    
    // 5. 验证推理完成
    expect(await inferencePage.isInferenceComplete(), '推理应该完成').toBe(true);
    
    // 6. 验证有输出结果
    expect(await inferencePage.hasOutputResult(), '应该有推理输出').toBe(true);
    
    // 7. 截图记录
    await inferencePage.screenshot('inference-complete');
    
    console.log('完整推理流程测试通过');
  });

  test('使用不同测试图片进行推理', async () => {
    // 加载模型
    await inferencePage.loadDefaultModel();
    expect(await inferencePage.isModelLoaded()).toBe(true);
    
    // 测试不同的图片
    const testImages = [
      'tests/assets/test.png',
      'tests/assets/test_224.png',
      'tests/assets/test_gray.png',
      'tests/assets/solid_red.png'
    ];
    
    for (const imagePath of testImages) {
      const fullPath = path.join(process.cwd(), imagePath);
      
      console.log(`测试图片: ${imagePath}`);
      
      // 上传图片
      await inferencePage.uploadImageFile(fullPath);
      expect(await inferencePage.isImageLoaded(), `${imagePath} 应该加载成功`).toBe(true);
      
      // 等待推理准备就绪
      await inferencePage.waitForInferenceReady();
      
      // 执行推理
      await inferencePage.runInference();
      
      // 验证推理完成
      expect(await inferencePage.isInferenceComplete(), `${imagePath} 推理应该完成`).toBe(true);
      
      // 验证有输出结果
      expect(await inferencePage.hasOutputResult(), `${imagePath} 应该有推理输出`).toBe(true);
      
      // 获取性能指标
      const metrics = await inferencePage.getPerformanceMetrics();
      console.log(`${imagePath} 性能指标:`, metrics);
      
      // 清除结果，准备下一个测试
      await inferencePage.clearResults();
    }
  });

  test('推理性能指标验证', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 记录开始时间
    const startTime = Date.now();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 记录结束时间
    const endTime = Date.now();
    const actualTime = endTime - startTime;
    
    // 获取性能指标
    const metrics = await inferencePage.getPerformanceMetrics();
    
    console.log('推理性能指标:', metrics);
    console.log(`实际测量时间: ${actualTime}ms`);
    
    // 验证推理时间指标
    const inferenceTime = parseFloat(metrics.inferenceTime);
    if (!isNaN(inferenceTime)) {
      expect(inferenceTime, '推理时间应该大于0').toBeGreaterThan(0);
      expect(inferenceTime, '推理时间应该在合理范围内').toBeLessThan(10000); // 10秒
    }
    
    // 验证总推理次数
    const totalInferences = parseInt(metrics.totalInferences);
    if (!isNaN(totalInferences)) {
      expect(totalInferences, '总推理次数应该至少为1').toBeGreaterThanOrEqual(1);
    }
    
    // 验证内存使用情况
    if (metrics.memoryUsage && metrics.memoryUsage !== '--') {
      console.log('内存使用情况:', metrics.memoryUsage);
    }
  });

  test('连续多次推理测试', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    const inferenceCount = 3;
    const inferenceTimes: number[] = [];
    
    for (let i = 0; i < inferenceCount; i++) {
      console.log(`执行第 ${i + 1} 次推理`);
      
      const startTime = Date.now();
      await inferencePage.runInference();
      const endTime = Date.now();
      
      const inferenceTime = endTime - startTime;
      inferenceTimes.push(inferenceTime);
      
      // 验证推理完成
      expect(await inferencePage.isInferenceComplete(), `第 ${i + 1} 次推理应该完成`).toBe(true);
      
      // 验证有输出结果
      expect(await inferencePage.hasOutputResult(), `第 ${i + 1} 次推理应该有输出`).toBe(true);
      
      console.log(`第 ${i + 1} 次推理耗时: ${inferenceTime}ms`);
      
      // 短暂等待
      await inferencePage.page.waitForTimeout(500);
    }
    
    // 分析推理时间趋势
    const avgTime = inferenceTimes.reduce((a, b) => a + b, 0) / inferenceTimes.length;
    console.log(`平均推理时间: ${avgTime.toFixed(2)}ms`);
    console.log('所有推理时间:', inferenceTimes);
    
    // 验证推理时间稳定性（后续推理应该不会明显变慢）
    const firstTime = inferenceTimes[0];
    const lastTime = inferenceTimes[inferenceTimes.length - 1];
    const timeIncrease = (lastTime - firstTime) / firstTime;
    
    // 时间增长不应该超过50%
    expect(timeIncrease, '推理时间不应该显著增长').toBeLessThan(0.5);
  });

  test('推理状态变化监控', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 记录状态变化
    const statusChanges: string[] = [];
    const startTime = Date.now();
    
    // 监控状态变化
    const checkInterval = setInterval(async () => {
      try {
        const currentStatus = await inferencePage.getInferenceStatus();
        if (statusChanges.length === 0 || statusChanges[statusChanges.length - 1] !== currentStatus) {
          statusChanges.push(currentStatus);
          console.log(`推理状态变化 [${Date.now() - startTime}ms]:`, currentStatus);
        }
      } catch (error) {
        // 忽略检查过程中的错误
      }
    }, 50);
    
    // 执行推理
    await inferencePage.runInference();
    clearInterval(checkInterval);
    
    // 验证状态变化
    expect(statusChanges.length, '应该有状态变化').toBeGreaterThan(0);
    
    // 最终状态应该是完成
    const finalStatus = statusChanges[statusChanges.length - 1];
    expect(finalStatus, '最终状态应该表示完成').toContain('完成');
    
    console.log('完整推理状态变化序列:', statusChanges);
  });

  test('推理输出数据结构验证', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取输出canvas数据
    const outputData = await inferencePage.getOutputCanvasData();
    
    // 验证输出数据结构
    expect(outputData, '输出数据应该存在').toBeTruthy();
    expect(outputData.data, '输出数据应该有data属性').toBeTruthy();
    expect(outputData.width, '输出数据应该有width属性').toBeGreaterThan(0);
    expect(outputData.height, '输出数据应该有height属性').toBeGreaterThan(0);
    
    // 验证数据内容
    const { data, width, height } = outputData;
    expect(data.length, '数据长度应该等于width*height*4').toBe(width * height * 4);
    
    // 检查是否有非透明像素（确认有实际输出）
    let hasNonTransparentPixels = false;
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] > 0) {
        hasNonTransparentPixels = true;
        break;
      }
    }
    
    expect(hasNonTransparentPixels, '输出应该包含非透明像素').toBe(true);
    
    console.log(`输出数据尺寸: ${width}x${height}`);
    console.log(`数据长度: ${data.length}`);
    
    // 分析像素值分布
    const pixelStats = {
      totalPixels: width * height,
      nonZeroAlpha: 0,
      avgRed: 0,
      avgGreen: 0,
      avgBlue: 0
    };
    
    let redSum = 0, greenSum = 0, blueSum = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      redSum += data[i];
      greenSum += data[i + 1];
      blueSum += data[i + 2];
      
      if (data[i + 3] > 0) {
        pixelStats.nonZeroAlpha++;
      }
    }
    
    pixelStats.avgRed = redSum / pixelStats.totalPixels;
    pixelStats.avgGreen = greenSum / pixelStats.totalPixels;
    pixelStats.avgBlue = blueSum / pixelStats.totalPixels;
    
    console.log('像素统计:', pixelStats);
  });

  test('不同模型的推理比较', async () => {
    const modelTests = [
      { name: '默认模型', loadMethod: () => inferencePage.loadDefaultModel() },
      { name: '简单模型', loadMethod: () => inferencePage.loadSimpleModel() }
    ];
    
    const results = [];
    
    for (const modelTest of modelTests) {
      console.log(`测试 ${modelTest.name}`);
      
      // 加载模型
      await modelTest.loadMethod();
      expect(await inferencePage.isModelLoaded(), `${modelTest.name} 应该加载成功`).toBe(true);
      
      // 加载图片
      await inferencePage.loadDefaultImage();
      await inferencePage.waitForInferenceReady();
      
      // 执行推理
      const startTime = Date.now();
      await inferencePage.runInference();
      const endTime = Date.now();
      
      // 收集结果
      const metrics = await inferencePage.getPerformanceMetrics();
      const hasOutput = await inferencePage.hasOutputResult();
      
      results.push({
        model: modelTest.name,
        inferenceTime: endTime - startTime,
        hasOutput,
        metrics
      });
      
      console.log(`${modelTest.name} 推理结果:`, {
        时间: `${endTime - startTime}ms`,
        有输出: hasOutput,
        性能指标: metrics
      });
      
      // 清除结果
      await inferencePage.clearResults();
    }
    
    // 验证所有模型都能成功推理
    for (const result of results) {
      expect(result.hasOutput, `${result.model} 应该有推理输出`).toBe(true);
      expect(result.inferenceTime, `${result.model} 推理时间应该合理`).toBeLessThan(30000);
    }
    
    console.log('所有模型推理结果比较:', results);
  });

  test('推理过程中内存变化监控', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 获取初始内存信息
    const initialMemory = await inferencePage.getMemoryInfo();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取推理后内存信息
    const finalMemory = await inferencePage.getMemoryInfo();
    
    if (initialMemory && finalMemory) {
      const memoryChange = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      
      console.log('推理前内存:', (initialMemory.usedJSHeapSize / 1024 / 1024).toFixed(2), 'MB');
      console.log('推理后内存:', (finalMemory.usedJSHeapSize / 1024 / 1024).toFixed(2), 'MB');
      console.log('内存变化:', (memoryChange / 1024 / 1024).toFixed(2), 'MB');
      
      // 内存变化应该在合理范围内
      const memoryChangeMB = Math.abs(memoryChange) / 1024 / 1024;
      expect(memoryChangeMB, '推理过程内存变化应该在合理范围内').toBeLessThan(100); // 100MB
    } else {
      console.log('当前浏览器不支持内存监控API');
    }
  });

  test('推理结果一致性验证', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行第一次推理
    await inferencePage.runInference();
    const firstOutput = await inferencePage.getOutputCanvasData();
    
    // 清除结果
    await inferencePage.clearResults();
    
    // 重新加载相同的图片
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行第二次推理
    await inferencePage.runInference();
    const secondOutput = await inferencePage.getOutputCanvasData();
    
    // 比较两次推理结果
    expect(firstOutput.width, '两次推理输出宽度应该相同').toBe(secondOutput.width);
    expect(firstOutput.height, '两次推理输出高度应该相同').toBe(secondOutput.height);
    expect(firstOutput.data.length, '两次推理输出数据长度应该相同').toBe(secondOutput.data.length);
    
    // 计算像素差异
    let diffPixels = 0;
    const totalPixels = firstOutput.width * firstOutput.height;
    
    for (let i = 0; i < firstOutput.data.length; i += 4) {
      const r1 = firstOutput.data[i];
      const g1 = firstOutput.data[i + 1];
      const b1 = firstOutput.data[i + 2];
      const a1 = firstOutput.data[i + 3];
      
      const r2 = secondOutput.data[i];
      const g2 = secondOutput.data[i + 1];
      const b2 = secondOutput.data[i + 2];
      const a2 = secondOutput.data[i + 3];
      
      if (Math.abs(r1 - r2) > 1 || Math.abs(g1 - g2) > 1 || 
          Math.abs(b1 - b2) > 1 || Math.abs(a1 - a2) > 1) {
        diffPixels++;
      }
    }
    
    const diffPercentage = (diffPixels / totalPixels) * 100;
    console.log(`像素差异: ${diffPixels}/${totalPixels} (${diffPercentage.toFixed(2)}%)`);
    
    // 两次推理结果应该基本一致（允许少量差异）
    expect(diffPercentage, '两次推理结果差异应该很小').toBeLessThan(5); // 5%以内的差异
  });
});