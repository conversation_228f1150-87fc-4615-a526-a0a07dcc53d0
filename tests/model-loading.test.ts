import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';
import path from 'path';

/**
 * ONNX模型加载测试套件
 * 验证不同ONNX模型的加载功能和兼容性
 */
test.describe('ONNX模型加载测试', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('加载默认ONNX模型', async () => {
    // 执行模型加载
    await inferencePage.loadDefaultModel();
    
    // 验证加载状态
    const modelStatus = await inferencePage.getModelStatus();
    expect(modelStatus, '模型应该加载成功').toContain('成功');
    
    // 验证模型已加载标记
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded, '模型加载标记应该为true').toBe(true);
    
    // 验证模型信息显示
    const modelInfo = await inferencePage.getModelInfo();
    console.log('默认模型信息:', modelInfo);
    
    // 截图记录
    await inferencePage.screenshot('default-model-loaded');
  });

  test('加载简单测试模型', async () => {
    // 执行简单模型加载
    await inferencePage.loadSimpleModel();
    
    // 验证加载状态
    const modelStatus = await inferencePage.getModelStatus();
    expect(modelStatus, '简单模型应该加载成功').toContain('成功');
    
    // 验证模型已加载标记
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded, '模型加载标记应该为true').toBe(true);
    
    console.log('简单模型状态:', modelStatus);
    
    // 截图记录
    await inferencePage.screenshot('simple-model-loaded');
  });

  test('通过文件上传加载模型', async () => {
    // 构建模型文件路径
    const modelPath = path.join(process.cwd(), 'public', 'model.onnx');
    
    // 上传模型文件
    await inferencePage.uploadModelFile(modelPath);
    
    // 验证加载状态
    const modelStatus = await inferencePage.getModelStatus();
    expect(modelStatus, '上传的模型应该加载成功').toContain('成功');
    
    // 验证模型已加载标记
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded, '模型加载标记应该为true').toBe(true);
    
    console.log('上传模型状态:', modelStatus);
  });

  test('验证模型信息显示', async () => {
    // 加载默认模型
    await inferencePage.loadDefaultModel();
    
    // 等待模型加载完成
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded).toBe(true);
    
    // 获取模型信息
    const modelInfo = await inferencePage.getModelInfo();
    
    if (modelInfo) {
      // 模型信息应该不为空
      expect(modelInfo.length, '模型信息应该存在').toBeGreaterThan(0);
      
      console.log('详细模型信息:', modelInfo);
      
      // 检查常见的模型信息字段
      const infoLower = modelInfo.toLowerCase();
      const hasRelevantInfo = infoLower.includes('input') || 
                             infoLower.includes('output') || 
                             infoLower.includes('shape') ||
                             infoLower.includes('输入') ||
                             infoLower.includes('输出') ||
                             infoLower.includes('形状');
      
      expect(hasRelevantInfo, '模型信息应该包含相关内容').toBe(true);
    } else {
      console.log('模型信息未显示或不可见');
    }
  });

  test('模型加载状态变化监控', async () => {
    // 记录初始状态
    const initialStatus = await inferencePage.getModelStatus();
    console.log('初始模型状态:', initialStatus);
    
    // 开始加载模型
    const loadPromise = inferencePage.loadDefaultModel();
    
    // 监控状态变化
    let statusChanges: string[] = [];
    const startTime = Date.now();
    
    // 周期性检查状态
    const checkInterval = setInterval(async () => {
      try {
        const currentStatus = await inferencePage.getModelStatus();
        if (statusChanges.length === 0 || statusChanges[statusChanges.length - 1] !== currentStatus) {
          statusChanges.push(currentStatus);
          console.log(`状态变化 [${Date.now() - startTime}ms]:`, currentStatus);
        }
      } catch (error) {
        // 忽略检查过程中的错误
      }
    }, 100);
    
    // 等待加载完成
    await loadPromise;
    clearInterval(checkInterval);
    
    // 验证最终状态
    const finalStatus = await inferencePage.getModelStatus();
    expect(finalStatus, '最终状态应该是成功').toContain('成功');
    
    // 验证状态变化
    expect(statusChanges.length, '应该有状态变化').toBeGreaterThan(0);
    console.log('完整状态变化序列:', statusChanges);
  });

  test('模型加载后推理按钮状态', async () => {
    // 检查初始按钮状态
    const initialButtonState = await inferencePage.isInferenceButtonEnabled();
    console.log('初始推理按钮状态:', initialButtonState);
    
    // 加载模型
    await inferencePage.loadDefaultModel();
    
    // 等待模型加载完成
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded).toBe(true);
    
    // 这时推理按钮可能还不可用，因为需要图片
    const buttonStateAfterModel = await inferencePage.isInferenceButtonEnabled();
    console.log('模型加载后推理按钮状态:', buttonStateAfterModel);
    
    // 加载默认图片
    await inferencePage.loadDefaultImage();
    
    // 等待推理准备就绪
    await inferencePage.waitForInferenceReady();
    
    // 验证按钮现在应该可用
    const finalButtonState = await inferencePage.isInferenceButtonEnabled();
    expect(finalButtonState, '推理按钮应该可用').toBe(true);
  });

  test('同时加载多个模型的行为', async () => {
    // 首先加载默认模型
    await inferencePage.loadDefaultModel();
    const firstModelStatus = await inferencePage.getModelStatus();
    
    // 然后加载简单模型（应该替换之前的模型）
    await inferencePage.loadSimpleModel();
    const secondModelStatus = await inferencePage.getModelStatus();
    
    // 验证两次加载都成功
    expect(firstModelStatus, '第一个模型应该加载成功').toContain('成功');
    expect(secondModelStatus, '第二个模型应该加载成功').toContain('成功');
    
    // 验证最终状态
    const isLoaded = await inferencePage.isModelLoaded();
    expect(isLoaded, '最终应该有模型加载').toBe(true);
    
    console.log('第一个模型状态:', firstModelStatus);
    console.log('第二个模型状态:', secondModelStatus);
  });

  test('验证模型文件路径正确性', async () => {
    // 检查public目录下的模型文件
    const modelExists = await inferencePage.page.evaluate(async () => {
      try {
        const response = await fetch('/model.onnx');
        return response.ok;
      } catch {
        return false;
      }
    });
    
    console.log('默认模型文件可访问性:', modelExists);
    
    // 检查简单模型文件
    const simpleModelExists = await inferencePage.page.evaluate(async () => {
      try {
        const response = await fetch('/simple_add.onnx');
        return response.ok;
      } catch {
        return false;
      }
    });
    
    console.log('简单模型文件可访问性:', simpleModelExists);
    
    // 至少默认模型应该存在
    expect(modelExists, '默认模型文件应该可访问').toBe(true);
  });

  test('模型加载性能监控', async () => {
    // 记录开始时间
    const startTime = Date.now();
    
    // 获取初始内存信息
    const initialMemory = await inferencePage.getMemoryInfo();
    
    // 加载模型
    await inferencePage.loadDefaultModel();
    
    // 记录结束时间
    const endTime = Date.now();
    const loadTime = endTime - startTime;
    
    // 获取加载后内存信息
    const finalMemory = await inferencePage.getMemoryInfo();
    
    // 验证加载时间合理
    expect(loadTime, '模型加载时间应该在合理范围内').toBeLessThan(30000); // 30秒
    
    console.log(`模型加载耗时: ${loadTime}ms`);
    
    if (initialMemory && finalMemory) {
      const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      console.log(`内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // 内存增长应该是合理的（模型加载会占用内存）
      expect(memoryIncrease, '内存增长应该大于0').toBeGreaterThan(0);
    }
  });

  test('检查模型加载后的WebGL状态', async () => {
    // 获取加载前的WebGL信息
    const beforeWebGL = await inferencePage.getWebGLInfo();
    
    // 加载模型
    await inferencePage.loadDefaultModel();
    
    // 获取加载后的WebGL信息
    const afterWebGL = await inferencePage.getWebGLInfo();
    
    // WebGL应该仍然可用
    expect(afterWebGL.supported, '模型加载后WebGL应该仍然可用').toBe(true);
    
    // 基本信息应该一致
    expect(afterWebGL.version, 'WebGL版本应该一致').toBe(beforeWebGL.version);
    expect(afterWebGL.renderer, 'WebGL渲染器应该一致').toBe(beforeWebGL.renderer);
    
    console.log('模型加载前后WebGL状态一致性验证通过');
  });
});