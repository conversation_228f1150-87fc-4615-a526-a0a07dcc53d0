import { test, expect } from '@playwright/test';

test.describe('基础功能验证', () => {
  test('模型文件访问测试', async ({ page }) => {
    // 直接检查模型文件
    const response = await page.request.get('/model.onnx');
    expect(response.status()).toBe(200);
    
    const contentLength = response.headers()['content-length'];
    expect(parseInt(contentLength || '0')).toBeGreaterThan(0);
    
    console.log(`模型文件大小: ${contentLength} bytes`);
  });

  test('推理库加载测试', async ({ page }) => {
    await page.goto('/test.html');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    await expect(page.locator('h1')).toContainText('WebGL ONNX 推理库快速测试');
    
    // 运行快速测试
    await page.click('button:has-text("运行快速测试")');
    
    // 等待测试完成
    await page.waitForTimeout(10000);
    
    // 检查日志内容
    const log = await page.locator('#log').textContent();
    console.log('测试日志:', log);
    
    // 验证是否有成功的步骤
    expect(log).toContain('模型文件下载成功');
    expect(log).toContain('推理库导入成功');
  });

  test('WebGL支持检查', async ({ page }) => {
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查WebGL状态
    const webglInfo = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      
      if (!gl) return { supported: false };
      
      return {
        supported: true,
        maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
        extensions: gl.getSupportedExtensions()?.length || 0
      };
    });
    
    console.log('WebGL信息:', webglInfo);
    expect(webglInfo.supported).toBe(true);
    expect(webglInfo.maxTextureSize).toBeGreaterThanOrEqual(512);
  });
});