import { test, expect } from '@playwright/test';

test('调试ONNX模型解析控制台输出', async ({ page }) => {
  // 监听控制台消息
  const consoleMessages: string[] = [];
  
  page.on('console', msg => {
    const text = `[${msg.type()}] ${msg.text()}`;
    consoleMessages.push(text);
    console.log('浏览器控制台:', text);
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('页面错误:', error.message);
    console.log('错误堆栈:', error.stack);
  });
  
  await page.goto('/debug.html');
  
  // 等待足够长的时间让所有异步操作完成
  await page.waitForTimeout(10000);
  
  // 输出收集到的所有控制台消息
  console.log('=== 所有控制台消息 ===');
  consoleMessages.forEach(msg => console.log(msg));
  
  // 检查页面内容
  const logContent = await page.locator('#log').textContent();
  console.log('=== 页面日志内容 ===');
  console.log(logContent);
});