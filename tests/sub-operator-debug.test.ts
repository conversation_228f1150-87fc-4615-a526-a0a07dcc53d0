import { test, expect } from '@playwright/test';

test.describe('Sub操作符修复验证测试', () => {
  test.beforeEach(async ({ page }) => {
    // 监听控制台消息
    page.on('console', msg => {
      console.log(`浏览器控制台 [${msg.type()}]: ${msg.text()}`);
    });
    
    // 监听错误
    page.on('pageerror', error => {
      console.log(`页面错误: ${error.message}`);
    });
  });

  test('复现Sub操作符错误 - 默认模型推理', async ({ page }) => {
    console.log('🧪 开始测试：复现Sub操作符错误');
    
    // 访问主页
    await page.goto('http://localhost:5173/');
    await page.waitForLoadState('networkidle');
    console.log('✅ 页面加载完成');

    // 等待页面初始化
    await page.waitForSelector('#webgl-status', { timeout: 10000 });
    
    // 检查WebGL状态
    const webglStatus = await page.textContent('#webgl-status');
    console.log(`WebGL状态: ${webglStatus}`);
    
    // 等待WebGL检查完成
    await page.waitForFunction(() => {
      const element = document.querySelector('#webgl-status');
      return element && element.textContent?.includes('✅');
    }, { timeout: 15000 });
    
    console.log('✅ WebGL兼容性检查通过');

    // 点击加载默认模型
    console.log('📦 正在加载默认模型...');
    await page.click('button:has-text("加载默认模型")');
    
    // 等待模型加载状态变化
    await page.waitForFunction(() => {
      const element = document.querySelector('#model-status');
      return element && (
        element.textContent?.includes('✅') || 
        element.textContent?.includes('❌') ||
        element.textContent?.includes('⚠️')
      );
    }, { timeout: 30000 });
    
    const modelStatus = await page.textContent('#model-status');
    console.log(`模型加载状态: ${modelStatus}`);
    
    // 检查是否出现Sub操作符错误
    if (modelStatus?.includes('❌') || modelStatus?.includes('Unsupported operations')) {
      console.log('❌ 检测到模型加载失败，可能是Sub操作符问题');
      
      // 获取详细错误信息
      const errorDetails = await page.evaluate(() => {
        const element = document.querySelector('#model-status');
        return element?.textContent || '';
      });
      
      console.log(`详细错误: ${errorDetails}`);
      
      // 如果错误中包含Sub，则说明修复未生效
      if (errorDetails.includes('Sub') || errorDetails.includes('sub_node')) {
        throw new Error(`Sub操作符修复未生效: ${errorDetails}`);
      }
    }
    
    // 如果模型加载成功，继续测试推理
    if (modelStatus?.includes('✅')) {
      console.log('✅ 默认模型加载成功，继续测试推理');
      
      // 点击加载默认图片
      console.log('🖼️ 正在加载默认图片...');
      await page.click('button:has-text("加载默认图片")');
      await page.waitForTimeout(2000);
      
      // 检查推理按钮是否启用
      const inferenceButton = page.locator('#inference-btn');
      await expect(inferenceButton).not.toBeDisabled({ timeout: 5000 });
      console.log('✅ 推理按钮已启用');
      
      // 点击开始推理
      console.log('🚀 开始推理...');
      await inferenceButton.click();
      
      // 等待推理结果
      await page.waitForFunction(() => {
        const element = document.querySelector('#inference-status');
        return element && (
          element.textContent?.includes('✅') || 
          element.textContent?.includes('❌')
        );
      }, { timeout: 30000 });
      
      const inferenceStatus = await page.textContent('#inference-status');
      console.log(`推理状态: ${inferenceStatus}`);
      
      // 检查是否出现Sub操作符推理错误
      if (inferenceStatus?.includes('❌')) {
        if (inferenceStatus.includes('sub_node') || inferenceStatus.includes('No program for node')) {
          throw new Error(`Sub操作符推理错误: ${inferenceStatus}`);
        }
        throw new Error(`推理失败: ${inferenceStatus}`);
      }
      
      if (inferenceStatus?.includes('✅')) {
        console.log('🎉 推理成功完成！Sub操作符修复验证通过');
      }
    }
  });

  test('测试简单Sub模型加载', async ({ page }) => {
    console.log('🧪 开始测试：简单Sub模型加载');
    
    await page.goto('http://localhost:5173/');
    await page.waitForLoadState('networkidle');
    
    // 等待页面初始化
    await page.waitForSelector('#webgl-status');
    await page.waitForFunction(() => {
      const element = document.querySelector('#webgl-status');
      return element && element.textContent?.includes('✅');
    }, { timeout: 15000 });
    
    // 点击加载Sub测试模型（如果按钮存在）
    const subTestButton = page.locator('button:has-text("Sub测试模型")');
    const buttonExists = await subTestButton.count() > 0;
    
    if (buttonExists) {
      console.log('📦 正在加载Sub测试模型...');
      await subTestButton.click();
      
      await page.waitForFunction(() => {
        const element = document.querySelector('#model-status');
        return element && (
          element.textContent?.includes('✅') || 
          element.textContent?.includes('❌')
        );
      }, { timeout: 30000 });
      
      const modelStatus = await page.textContent('#model-status');
      console.log(`Sub模型加载状态: ${modelStatus}`);
      
      if (modelStatus?.includes('❌')) {
        throw new Error(`Sub测试模型加载失败: ${modelStatus}`);
      }
      
      if (modelStatus?.includes('✅')) {
        console.log('✅ Sub测试模型加载成功');
      }
    } else {
      console.log('⚠️ 未找到Sub测试模型按钮，跳过此测试');
    }
  });

  test('验证操作符支持情况', async ({ page }) => {
    console.log('🧪 开始测试：验证操作符支持情况');
    
    await page.goto('http://localhost:5174/test_sub_fix.html');
    await page.waitForLoadState('networkidle');
    
    // 点击测试按钮
    await page.click('button:has-text("测试Sub操作符")');
    
    // 等待测试完成
    await page.waitForFunction(() => {
      const log = document.querySelector('#log');
      return log && (
        log.textContent?.includes('测试总结') ||
        log.textContent?.includes('所有测试通过') ||
        log.textContent?.includes('部分测试失败')
      );
    }, { timeout: 60000 });
    
    // 获取测试结果
    const logContent = await page.textContent('#log');
    console.log('测试日志内容:');
    console.log(logContent);
    
    // 检查是否有测试结果元素
    const testResults = await page.locator('#test-results .test-result').count();
    console.log(`测试结果数量: ${testResults}`);
    
    if (testResults > 0) {
      for (let i = 0; i < testResults; i++) {
        const result = await page.locator('#test-results .test-result').nth(i).textContent();
        console.log(`测试结果 ${i + 1}: ${result}`);
      }
    }
    
    // 验证是否通过所有测试
    if (logContent?.includes('所有测试通过')) {
      console.log('🎉 所有测试通过！Sub操作符修复成功');
    } else if (logContent?.includes('部分测试失败')) {
      console.log('⚠️ 部分测试失败，需要进一步调试');
      
      // 如果包含Sub相关错误，抛出异常
      if (logContent?.includes('Sub') && logContent?.includes('失败')) {
        throw new Error('Sub操作符测试失败');
      }
    }
  });
});