import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';
import path from 'path';
import fs from 'fs';

/**
 * 推理结果验证和性能测试套件
 * 验证推理输出的正确性和系统性能表现
 */
test.describe('推理结果验证和性能测试', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('推理输出格式验证', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取输出数据
    const outputData = await inferencePage.getOutputCanvasData();
    
    // 验证输出数据结构
    expect(outputData, '输出数据应该存在').toBeTruthy();
    expect(outputData.data, '输出数据应该有data属性').toBeInstanceOf(Uint8ClampedArray);
    expect(outputData.width, '输出宽度应该大于0').toBeGreaterThan(0);
    expect(outputData.height, '输出高度应该大于0').toBeGreaterThan(0);
    
    // 验证数据完整性
    const expectedLength = outputData.width * outputData.height * 4;
    expect(outputData.data.length, '数据长度应该匹配尺寸').toBe(expectedLength);
    
    // 验证像素值范围
    for (let i = 0; i < outputData.data.length; i++) {
      const pixelValue = outputData.data[i];
      expect(pixelValue, `像素值应该在0-255范围内: index ${i}`).toBeGreaterThanOrEqual(0);
      expect(pixelValue, `像素值应该在0-255范围内: index ${i}`).toBeLessThanOrEqual(255);
    }
    
    console.log(`输出验证通过: ${outputData.width}x${outputData.height}, 数据长度: ${outputData.data.length}`);
  });

  test('灰度图输出验证', async () => {
    // 根据演示页面，输出应该是灰度图
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取输出数据
    const outputData = await inferencePage.getOutputCanvasData();
    
    // 分析输出是否为灰度图
    let grayscalePixels = 0;
    let totalPixels = 0;
    
    for (let i = 0; i < outputData.data.length; i += 4) {
      const r = outputData.data[i];
      const g = outputData.data[i + 1];
      const b = outputData.data[i + 2];
      const a = outputData.data[i + 3];
      
      // 只统计非透明像素
      if (a > 0) {
        totalPixels++;
        
        // 检查是否为灰度（R=G=B）
        if (r === g && g === b) {
          grayscalePixels++;
        }
      }
    }
    
    const grayscalePercentage = totalPixels > 0 ? (grayscalePixels / totalPixels) * 100 : 0;
    
    console.log(`灰度像素比例: ${grayscalePixels}/${totalPixels} (${grayscalePercentage.toFixed(2)}%)`);
    
    // 大部分像素应该是灰度的（因为输出是灰度图）
    expect(grayscalePercentage, '输出应该主要是灰度像素').toBeGreaterThan(80);
  });

  test('数值精度验证', async () => {
    // 加载预期结果数据
    const expectedResultsPath = path.join(process.cwd(), 'tests/fixtures/expected_results.json');
    let expectedResults = {};
    
    if (fs.existsSync(expectedResultsPath)) {
      const expectedResultsContent = fs.readFileSync(expectedResultsPath, 'utf-8');
      expectedResults = JSON.parse(expectedResultsContent);
    }
    
    // 使用标准测试图片
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取输出数据
    const outputData = await inferencePage.getOutputCanvasData();
    
    // 计算基本统计信息
    const stats = {
      mean: 0,
      min: 255,
      max: 0,
      nonZeroPixels: 0
    };
    
    let sum = 0;
    let pixelCount = 0;
    
    for (let i = 0; i < outputData.data.length; i += 4) {
      const r = outputData.data[i];
      const a = outputData.data[i + 3];
      
      // 只统计非透明像素的R通道（灰度值）
      if (a > 0) {
        sum += r;
        pixelCount++;
        stats.min = Math.min(stats.min, r);
        stats.max = Math.max(stats.max, r);
        
        if (r > 0) {
          stats.nonZeroPixels++;
        }
      }
    }
    
    if (pixelCount > 0) {
      stats.mean = sum / pixelCount;
    }
    
    console.log('输出统计信息:', stats);
    
    // 验证统计信息合理性
    expect(stats.min, '最小值应该在合理范围内').toBeGreaterThanOrEqual(0);
    expect(stats.max, '最大值应该在合理范围内').toBeLessThanOrEqual(255);
    expect(stats.mean, '平均值应该在合理范围内').toBeGreaterThanOrEqual(0);
    expect(stats.mean, '平均值应该在合理范围内').toBeLessThanOrEqual(255);
    expect(stats.nonZeroPixels, '应该有非零像素').toBeGreaterThan(0);
  });

  test('边界条件输出验证', async () => {
    // 测试极小图片
    await inferencePage.loadDefaultModel();
    
    const edgeImagePath = path.join(process.cwd(), 'tests/assets/edge_1x1.png');
    await inferencePage.uploadImageFile(edgeImagePath);
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 验证推理完成
    expect(await inferencePage.isInferenceComplete(), '边界条件推理应该完成').toBe(true);
    
    // 验证有输出
    expect(await inferencePage.hasOutputResult(), '边界条件应该有输出').toBe(true);
    
    console.log('边界条件测试通过');
  });

  test('推理性能基准测试', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行基准测试
    await inferencePage.runBenchmark();
    
    // 获取基准测试结果
    const status = await inferencePage.getInferenceStatus();
    expect(status, '基准测试应该完成').toContain('基准测试完成');
    
    // 获取性能指标
    const metrics = await inferencePage.getPerformanceMetrics();
    console.log('基准测试性能指标:', metrics);
    
    // 验证性能指标
    const inferenceTime = parseFloat(metrics.inferenceTime);
    if (!isNaN(inferenceTime)) {
      expect(inferenceTime, '推理时间应该在合理范围内').toBeLessThan(5000); // 5秒
    }
    
    const fps = parseFloat(metrics.fps);
    if (!isNaN(fps)) {
      expect(fps, 'FPS应该大于0').toBeGreaterThan(0);
    }
    
    const totalInferences = parseInt(metrics.totalInferences);
    if (!isNaN(totalInferences)) {
      expect(totalInferences, '总推理次数应该增加').toBeGreaterThan(1);
    }
  });

  test('内存性能监控', async () => {
    // 获取初始内存
    const initialMemory = await inferencePage.getMemoryInfo();
    
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行多次推理来测试内存泄漏
    const inferenceCount = 5;
    const memorySnapshots = [];
    
    for (let i = 0; i < inferenceCount; i++) {
      await inferencePage.runInference();
      
      const memory = await inferencePage.getMemoryInfo();
      if (memory) {
        memorySnapshots.push({
          iteration: i + 1,
          usedMemory: memory.usedJSHeapSize / 1024 / 1024 // MB
        });
      }
      
      // 清除结果
      await inferencePage.clearResults();
      
      // 等待垃圾回收
      await inferencePage.page.waitForTimeout(1000);
    }
    
    console.log('内存使用情况:', memorySnapshots);
    
    if (memorySnapshots.length > 1) {
      const firstMemory = memorySnapshots[0].usedMemory;
      const lastMemory = memorySnapshots[memorySnapshots.length - 1].usedMemory;
      const memoryIncrease = lastMemory - firstMemory;
      
      console.log(`内存变化: ${firstMemory.toFixed(2)}MB -> ${lastMemory.toFixed(2)}MB (增长: ${memoryIncrease.toFixed(2)}MB)`);
      
      // 内存增长应该在合理范围内（避免内存泄漏）
      expect(memoryIncrease, '内存增长应该在合理范围内').toBeLessThan(50); // 50MB
    }
  });

  test('不同尺寸图片性能比较', async () => {
    const testImages = [
      { name: '224x224', path: 'tests/assets/test_224.png' },
      { name: '512x512', path: 'tests/assets/test.png' },
      { name: '1024x1024', path: 'tests/assets/large_1024.png' }
    ];
    
    const performanceResults = [];
    
    await inferencePage.loadDefaultModel();
    
    for (const img of testImages) {
      const imagePath = path.join(process.cwd(), img.path);
      
      console.log(`测试图片尺寸: ${img.name}`);
      
      // 上传图片
      await inferencePage.uploadImageFile(imagePath);
      await inferencePage.waitForInferenceReady();
      
      // 记录推理时间
      const startTime = Date.now();
      await inferencePage.runInference();
      const endTime = Date.now();
      
      const inferenceTime = endTime - startTime;
      const metrics = await inferencePage.getPerformanceMetrics();
      
      performanceResults.push({
        size: img.name,
        inferenceTime,
        metrics
      });
      
      console.log(`${img.name} 推理时间: ${inferenceTime}ms`);
      
      // 清除结果
      await inferencePage.clearResults();
    }
    
    console.log('不同尺寸性能比较:', performanceResults);
    
    // 验证所有尺寸都能成功推理
    for (const result of performanceResults) {
      expect(result.inferenceTime, `${result.size} 推理时间应该合理`).toBeLessThan(30000);
    }
  });

  test('推理结果质量评估', async () => {
    // 使用标准测试图片
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取输出数据
    const outputData = await inferencePage.getOutputCanvasData();
    
    // 分析输出质量
    const qualityMetrics = {
      totalPixels: 0,
      nonZeroPixels: 0,
      histogram: new Array(256).fill(0),
      entropy: 0,
      contrast: 0
    };
    
    // 统计像素值分布
    for (let i = 0; i < outputData.data.length; i += 4) {
      const r = outputData.data[i];
      const a = outputData.data[i + 3];
      
      if (a > 0) {
        qualityMetrics.totalPixels++;
        if (r > 0) {
          qualityMetrics.nonZeroPixels++;
        }
        qualityMetrics.histogram[r]++;
      }
    }
    
    // 计算熵（图像复杂度指标）
    for (let i = 0; i < 256; i++) {
      if (qualityMetrics.histogram[i] > 0) {
        const probability = qualityMetrics.histogram[i] / qualityMetrics.totalPixels;
        qualityMetrics.entropy -= probability * Math.log2(probability);
      }
    }
    
    // 计算对比度
    let sum = 0;
    let sumSquares = 0;
    for (let i = 0; i < 256; i++) {
      const count = qualityMetrics.histogram[i];
      sum += i * count;
      sumSquares += i * i * count;
    }
    
    if (qualityMetrics.totalPixels > 0) {
      const mean = sum / qualityMetrics.totalPixels;
      const variance = (sumSquares / qualityMetrics.totalPixels) - (mean * mean);
      qualityMetrics.contrast = Math.sqrt(variance);
    }
    
    console.log('输出质量指标:', qualityMetrics);
    
    // 验证质量指标
    expect(qualityMetrics.nonZeroPixels, '应该有有效像素').toBeGreaterThan(0);
    expect(qualityMetrics.entropy, '熵应该大于0').toBeGreaterThan(0);
    expect(qualityMetrics.contrast, '对比度应该大于0').toBeGreaterThan(0);
    
    // 熵值应该在合理范围内（0-8位）
    expect(qualityMetrics.entropy, '熵值应该在合理范围内').toBeLessThanOrEqual(8);
  });

  test('WebGL资源使用监控', async () => {
    // 获取初始WebGL状态
    const initialWebGL = await inferencePage.getWebGLInfo();
    
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行推理
    await inferencePage.runInference();
    
    // 获取推理后WebGL状态
    const finalWebGL = await inferencePage.getWebGLInfo();
    
    // 验证WebGL状态一致性
    expect(finalWebGL.supported, 'WebGL应该仍然可用').toBe(true);
    expect(finalWebGL.version, 'WebGL版本应该一致').toBe(initialWebGL.version);
    expect(finalWebGL.maxTextureSize, '纹理尺寸限制应该一致').toBe(initialWebGL.maxTextureSize);
    
    console.log('WebGL资源使用监控通过');
  });

  test('批量推理吞吐量测试', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    const batchSize = 10;
    const startTime = Date.now();
    
    // 执行批量推理
    for (let i = 0; i < batchSize; i++) {
      await inferencePage.runInference();
      console.log(`批量推理进度: ${i + 1}/${batchSize}`);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTimePerInference = totalTime / batchSize;
    const throughput = (batchSize / totalTime) * 1000; // 每秒推理次数
    
    console.log(`批量推理统计:`);
    console.log(`- 总时间: ${totalTime}ms`);
    console.log(`- 平均每次: ${avgTimePerInference.toFixed(2)}ms`);
    console.log(`- 吞吐量: ${throughput.toFixed(2)} 推理/秒`);
    
    // 验证吞吐量合理
    expect(avgTimePerInference, '平均推理时间应该合理').toBeLessThan(10000); // 10秒
    expect(throughput, '吞吐量应该大于0').toBeGreaterThan(0);
    
    // 获取最终性能指标
    const finalMetrics = await inferencePage.getPerformanceMetrics();
    console.log('最终性能指标:', finalMetrics);
    
    const totalInferences = parseInt(finalMetrics.totalInferences);
    expect(totalInferences, '总推理次数应该匹配').toBeGreaterThanOrEqual(batchSize);
  });
});