import { Page } from '@playwright/test';

/**
 * 测试工具函数集合
 * 提供通用的测试辅助功能
 */

/**
 * 图像处理工具类
 */
export class ImageUtils {
  /**
   * 捕获Canvas数据并转换为ImageData
   */
  static async captureCanvasData(page: Page, canvasSelector: string): Promise<ImageData | null> {
    return await page.evaluate((selector) => {
      const canvas = document.querySelector(selector) as HTMLCanvasElement;
      if (!canvas) return null;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;
      
      return ctx.getImageData(0, 0, canvas.width, canvas.height);
    }, canvasSelector);
  }

  /**
   * 比较两个Canvas的输出
   */
  static async compareCanvasOutputs(
    page: Page, 
    canvas1Selector: string, 
    canvas2Selector: string,
    tolerance: number = 1
  ): Promise<{
    similarity: number;
    differentPixels: number;
    totalPixels: number;
  }> {
    return await page.evaluate(([selector1, selector2, tol]) => {
      const canvas1 = document.querySelector(selector1) as HTMLCanvasElement;
      const canvas2 = document.querySelector(selector2) as HTMLCanvasElement;
      
      if (!canvas1 || !canvas2) {
        return { similarity: 0, differentPixels: -1, totalPixels: 0 };
      }
      
      const ctx1 = canvas1.getContext('2d');
      const ctx2 = canvas2.getContext('2d');
      
      if (!ctx1 || !ctx2) {
        return { similarity: 0, differentPixels: -1, totalPixels: 0 };
      }
      
      const data1 = ctx1.getImageData(0, 0, canvas1.width, canvas1.height);
      const data2 = ctx2.getImageData(0, 0, canvas2.width, canvas2.height);
      
      if (data1.width !== data2.width || data1.height !== data2.height) {
        return { similarity: 0, differentPixels: -1, totalPixels: 0 };
      }
      
      let differentPixels = 0;
      const totalPixels = data1.width * data1.height;
      
      for (let i = 0; i < data1.data.length; i += 4) {
        const r1 = data1.data[i];
        const g1 = data1.data[i + 1];
        const b1 = data1.data[i + 2];
        const a1 = data1.data[i + 3];
        
        const r2 = data2.data[i];
        const g2 = data2.data[i + 1];
        const b2 = data2.data[i + 2];
        const a2 = data2.data[i + 3];
        
        if (Math.abs(r1 - r2) > tol || Math.abs(g1 - g2) > tol || 
            Math.abs(b1 - b2) > tol || Math.abs(a1 - a2) > tol) {
          differentPixels++;
        }
      }
      
      const similarity = ((totalPixels - differentPixels) / totalPixels) * 100;
      
      return { similarity, differentPixels, totalPixels };
    }, [canvas1Selector, canvas2Selector, tolerance]);
  }

  /**
   * 计算图像统计信息
   */
  static async calculateImageStats(page: Page, canvasSelector: string): Promise<{
    mean: number;
    min: number;
    max: number;
    stdDev: number;
    histogram: number[];
  } | null> {
    return await page.evaluate((selector) => {
      const canvas = document.querySelector(selector) as HTMLCanvasElement;
      if (!canvas) return null;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      let sum = 0;
      let min = 255;
      let max = 0;
      let pixelCount = 0;
      const histogram = new Array(256).fill(0);
      
      // 计算灰度值统计（使用R通道）
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const a = data[i + 3];
        
        // 只统计非透明像素
        if (a > 0) {
          sum += r;
          min = Math.min(min, r);
          max = Math.max(max, r);
          histogram[r]++;
          pixelCount++;
        }
      }
      
      if (pixelCount === 0) {
        return { mean: 0, min: 0, max: 0, stdDev: 0, histogram };
      }
      
      const mean = sum / pixelCount;
      
      // 计算标准差
      let variance = 0;
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const a = data[i + 3];
        
        if (a > 0) {
          variance += Math.pow(r - mean, 2);
        }
      }
      
      const stdDev = Math.sqrt(variance / pixelCount);
      
      return { mean, min, max, stdDev, histogram };
    }, canvasSelector);
  }

  /**
   * 检查图像是否为纯色
   */
  static async isPureSolidColor(page: Page, canvasSelector: string): Promise<boolean> {
    return await page.evaluate((selector) => {
      const canvas = document.querySelector(selector) as HTMLCanvasElement;
      if (!canvas) return false;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return false;
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      if (data.length === 0) return false;
      
      // 获取第一个非透明像素的颜色
      let firstR = -1, firstG = -1, firstB = -1;
      
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];
        
        if (a > 0) {
          if (firstR === -1) {
            firstR = r;
            firstG = g;
            firstB = b;
          } else {
            // 检查是否与第一个像素颜色相同
            if (r !== firstR || g !== firstG || b !== firstB) {
              return false;
            }
          }
        }
      }
      
      return true;
    }, canvasSelector);
  }
}

/**
 * 性能监控工具类
 */
export class PerformanceUtils {
  /**
   * 监控资源使用情况
   */
  static async monitorResourceUsage(page: Page): Promise<{
    memory?: any;
    timing: any;
    webglInfo?: any;
  }> {
    return await page.evaluate(() => {
      const result: any = {
        timing: performance.timing
      };
      
      // 内存信息
      if ('memory' in performance) {
        result.memory = {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        };
      }
      
      // WebGL信息
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
      
      if (gl) {
        result.webglInfo = {
          version: gl.getParameter(gl.VERSION),
          renderer: gl.getParameter(gl.RENDERER),
          vendor: gl.getParameter(gl.VENDOR),
          maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
          maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)
        };
      }
      
      return result;
    });
  }

  /**
   * 测量函数执行时间
   */
  static async measureExecutionTime<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; executionTime: number }> {
    const startTime = Date.now();
    const result = await operation();
    const endTime = Date.now();
    const executionTime = endTime - startTime;
    
    return { result, executionTime };
  }

  /**
   * 创建性能基准测试
   */
  static async createBenchmark<T>(
    operation: () => Promise<T>,
    iterations: number = 10
  ): Promise<{
    results: T[];
    times: number[];
    avgTime: number;
    minTime: number;
    maxTime: number;
    stdDev: number;
  }> {
    const results: T[] = [];
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      const { result, executionTime } = await this.measureExecutionTime(operation);
      results.push(result);
      times.push(executionTime);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    // 计算标准差
    const variance = times.reduce((acc, time) => acc + Math.pow(time - avgTime, 2), 0) / times.length;
    const stdDev = Math.sqrt(variance);
    
    return {
      results,
      times,
      avgTime,
      minTime,
      maxTime,
      stdDev
    };
  }
}

/**
 * 等待工具类
 */
export class WaitUtils {
  /**
   * 等待条件满足
   */
  static async waitForCondition(
    page: Page,
    condition: () => Promise<boolean>,
    timeout: number = 10000,
    interval: number = 100
  ): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await page.waitForTimeout(interval);
    }
    
    return false;
  }

  /**
   * 等待元素文本变化
   */
  static async waitForTextChange(
    page: Page,
    selector: string,
    initialText: string,
    timeout: number = 10000
  ): Promise<string | null> {
    return await page.waitForFunction(
      ([sel, initial]) => {
        const element = document.querySelector(sel);
        const currentText = element?.textContent || '';
        return currentText !== initial ? currentText : null;
      },
      [selector, initialText],
      { timeout }
    );
  }

  /**
   * 等待Canvas内容变化
   */
  static async waitForCanvasChange(
    page: Page,
    canvasSelector: string,
    timeout: number = 10000
  ): Promise<boolean> {
    return await page.waitForFunction(
      (selector) => {
        const canvas = document.querySelector(selector) as HTMLCanvasElement;
        if (!canvas) return false;
        
        const ctx = canvas.getContext('2d');
        if (!ctx) return false;
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // 检查是否有非透明像素
        for (let i = 3; i < data.length; i += 4) {
          if (data[i] > 0) return true;
        }
        return false;
      },
      canvasSelector,
      { timeout }
    );
  }
}

/**
 * 数据验证工具类
 */
export class ValidationUtils {
  /**
   * 验证ONNX模型信息格式
   */
  static validateModelInfo(modelInfo: string): {
    isValid: boolean;
    hasInputInfo: boolean;
    hasOutputInfo: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    let hasInputInfo = false;
    let hasOutputInfo = false;
    
    if (!modelInfo || modelInfo.trim().length === 0) {
      errors.push('模型信息为空');
      return { isValid: false, hasInputInfo, hasOutputInfo, errors };
    }
    
    const infoLower = modelInfo.toLowerCase();
    
    // 检查输入信息
    if (infoLower.includes('input') || infoLower.includes('输入')) {
      hasInputInfo = true;
    }
    
    // 检查输出信息
    if (infoLower.includes('output') || infoLower.includes('输出')) {
      hasOutputInfo = true;
    }
    
    // 检查形状信息
    const hasShapeInfo = infoLower.includes('shape') || 
                        infoLower.includes('形状') ||
                        infoLower.includes('[') ||
                        infoLower.includes('x');
    
    if (!hasShapeInfo) {
      errors.push('缺少形状信息');
    }
    
    const isValid = errors.length === 0 && (hasInputInfo || hasOutputInfo);
    
    return { isValid, hasInputInfo, hasOutputInfo, errors };
  }

  /**
   * 验证性能指标格式
   */
  static validatePerformanceMetrics(metrics: {
    inferenceTime: string;
    memoryUsage: string;
    fps: string;
    totalInferences: string;
  }): {
    isValid: boolean;
    validMetrics: string[];
    invalidMetrics: string[];
  } {
    const validMetrics: string[] = [];
    const invalidMetrics: string[] = [];
    
    // 验证推理时间
    const inferenceTime = parseFloat(metrics.inferenceTime);
    if (!isNaN(inferenceTime) && inferenceTime >= 0) {
      validMetrics.push('inferenceTime');
    } else if (metrics.inferenceTime !== '--') {
      invalidMetrics.push('inferenceTime');
    }
    
    // 验证FPS
    const fps = parseFloat(metrics.fps);
    if (!isNaN(fps) && fps >= 0) {
      validMetrics.push('fps');
    } else if (metrics.fps !== '--') {
      invalidMetrics.push('fps');
    }
    
    // 验证总推理次数
    const totalInferences = parseInt(metrics.totalInferences);
    if (!isNaN(totalInferences) && totalInferences >= 0) {
      validMetrics.push('totalInferences');
    } else if (metrics.totalInferences !== '0' && metrics.totalInferences !== '--') {
      invalidMetrics.push('totalInferences');
    }
    
    // 内存使用情况可能格式多样，暂时不严格验证
    if (metrics.memoryUsage && metrics.memoryUsage !== '--') {
      validMetrics.push('memoryUsage');
    }
    
    const isValid = invalidMetrics.length === 0 && validMetrics.length > 0;
    
    return { isValid, validMetrics, invalidMetrics };
  }
}

/**
 * 文件处理工具类
 */
export class FileUtils {
  /**
   * 检查文件是否存在且可访问
   */
  static async checkFileAccessibility(page: Page, filePath: string): Promise<{
    exists: boolean;
    accessible: boolean;
    size?: number;
    error?: string;
  }> {
    return await page.evaluate(async (path) => {
      try {
        const response = await fetch(path);
        if (response.ok) {
          const size = parseInt(response.headers.get('content-length') || '0');
          return {
            exists: true,
            accessible: true,
            size: size > 0 ? size : undefined
          };
        } else {
          return {
            exists: false,
            accessible: false,
            error: `HTTP ${response.status}: ${response.statusText}`
          };
        }
      } catch (error) {
        return {
          exists: false,
          accessible: false,
          error: error.toString()
        };
      }
    }, filePath);
  }

  /**
   * 生成测试报告摘要
   */
  static generateTestSummary(results: any[]): {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
    successRate: number;
  } {
    const total = results.length;
    const passed = results.filter(r => r.status === 'passed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    const skipped = results.filter(r => r.status === 'skipped').length;
    const duration = results.reduce((sum, r) => sum + (r.duration || 0), 0);
    const successRate = total > 0 ? (passed / total) * 100 : 0;
    
    return {
      total,
      passed,
      failed,
      skipped,
      duration,
      successRate
    };
  }
}