import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

/**
 * 推理演示页面对象
 * 封装推理库演示页面的所有操作
 */
export class InferencePage extends BasePage {
  // 页面元素选择器
  private readonly selectors = {
    // 系统检查
    webglStatus: '#webgl-status',
    checkSystemBtn: 'button:has-text("重新检查")',

    // 模型加载
    modelFileInput: '#model-file',
    loadDefaultModelBtn: 'button:has-text("加载默认模型")',
    loadSimpleModelBtn: 'button:has-text("加载简单测试模型")',
    modelStatus: '#model-status',
    modelInfo: '#model-info',
    modelDetails: '#model-details',

    // 图片输入
    imageFileInput: '#image-file',
    loadDefaultImageBtn: 'button:has-text("加载默认图片")',
    cameraBtn: '#camera-btn',
    inputCanvas: '#input-canvas',

    // 推理执行
    inferenceBtn: '#inference-btn',
    benchmarkBtn: '#benchmark-btn',
    clearResultsBtn: 'button:has-text("清除结果")',
    inferenceStatus: '#inference-status',
    progressContainer: '#progress-container',
    progressFill: '#progress-fill',

    // 结果展示
    outputCanvas: '#output-canvas',

    // 性能指标
    inferenceTime: '#inference-time',
    memoryUsage: '#memory-usage',
    fps: '#fps',
    totalInferences: '#total-inferences',

    // 摄像头模态框
    cameraModal: '#camera-modal',
    cameraVideo: '#camera-video',
    captureBtn: 'button:has-text("拍照")',
    closeCameraBtn: 'button:has-text("关闭")'
  };

  constructor(page: Page) {
    super(page);
  }

  /**
   * 导航到推理演示页面
   */
  async goto(): Promise<void> {
    await this.page.goto('/');
    await this.waitForLoad();
  }

  /**
   * 检查WebGL支持状态
   */
  async checkWebGLSupport(): Promise<string> {
    return await this.getText(this.selectors.webglStatus);
  }

  /**
   * 重新检查系统兼容性
   */
  async recheckSystemCapabilities(): Promise<void> {
    await this.safeClick(this.selectors.checkSystemBtn);
    await this.page.waitForTimeout(1000); // 等待检查完成
  }

  /**
   * 加载默认ONNX模型
   */
  async loadDefaultModel(): Promise<void> {
    await this.safeClick(this.selectors.loadDefaultModelBtn);

    // 等待模型加载完成
    await this.page.waitForFunction(() => {
      const status = document.querySelector('#model-status')?.textContent;
      return status?.includes('成功') || status?.includes('失败');
    }, { timeout: 30000 });
  }

  /**
   * 加载简单测试模型
   */
  async loadSimpleModel(): Promise<void> {
    await this.safeClick(this.selectors.loadSimpleModelBtn);

    // 等待模型加载完成
    await this.page.waitForFunction(() => {
      const status = document.querySelector('#model-status')?.textContent;
      return status?.includes('成功') || status?.includes('失败');
    }, { timeout: 30000 });
  }

  /**
   * 上传自定义模型文件
   */
  async uploadModelFile(filePath: string): Promise<void> {
    await this.page.setInputFiles(this.selectors.modelFileInput, filePath);

    // 等待模型加载完成
    await this.page.waitForFunction(() => {
      const status = document.querySelector('#model-status')?.textContent;
      return status?.includes('成功') || status?.includes('失败');
    }, { timeout: 30000 });
  }

  /**
   * 获取模型加载状态
   */
  async getModelStatus(): Promise<string> {
    return await this.getText(this.selectors.modelStatus);
  }

  /**
   * 获取模型信息
   */
  async getModelInfo(): Promise<string> {
    try {
      await this.waitForVisible(this.selectors.modelInfo);
      return await this.getText(this.selectors.modelDetails);
    } catch {
      return ''; // 模型信息不可见
    }
  }

  /**
   * 检查模型是否加载成功
   */
  async isModelLoaded(): Promise<boolean> {
    const status = await this.getModelStatus();
    return status.includes('成功');
  }

  /**
   * 加载默认测试图片
   */
  async loadDefaultImage(): Promise<void> {
    await this.safeClick(this.selectors.loadDefaultImageBtn);
    await this.page.waitForTimeout(1000); // 等待图片加载
  }

  /**
   * 上传自定义图片文件
   */
  async uploadImageFile(filePath: string): Promise<void> {
    await this.page.setInputFiles(this.selectors.imageFileInput, filePath);
    await this.page.waitForTimeout(1000); // 等待图片加载
  }

  /**
   * 检查输入图片是否已加载
   */
  async isImageLoaded(): Promise<boolean> {
    // 检查canvas是否有内容
    return await this.page.evaluate(() => {
      const canvas = document.querySelector('#input-canvas') as HTMLCanvasElement;
      if (!canvas) return false;

      const ctx = canvas.getContext('2d');
      if (!ctx) return false;

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // 检查是否有非透明像素
      for (let i = 3; i < data.length; i += 4) {
        if (data[i] > 0) return true;
      }
      return false;
    });
  }

  /**
   * 执行推理
   */
  async runInference(): Promise<void> {
    // 确保推理按钮可用
    await expect(this.page.locator(this.selectors.inferenceBtn)).toBeEnabled();

    await this.safeClick(this.selectors.inferenceBtn);

    // 等待推理完成
    await this.page.waitForFunction(() => {
      const status = document.querySelector('#inference-status')?.textContent;
      return status?.includes('完成') || status?.includes('失败');
    }, { timeout: 60000 });
  }

  /**
   * 运行性能基准测试
   */
  async runBenchmark(): Promise<void> {
    await expect(this.page.locator(this.selectors.benchmarkBtn)).toBeEnabled();
    await this.safeClick(this.selectors.benchmarkBtn);

    // 等待基准测试完成
    await this.page.waitForFunction(() => {
      const status = document.querySelector('#inference-status')?.textContent;
      return status?.includes('基准测试完成') || status?.includes('失败');
    }, { timeout: 120000 });
  }

  /**
   * 获取推理状态
   */
  async getInferenceStatus(): Promise<string> {
    return await this.getText(this.selectors.inferenceStatus);
  }

  /**
   * 检查推理是否成功
   */
  async isInferenceComplete(): Promise<boolean> {
    const status = await this.getInferenceStatus();
    return status.includes('完成');
  }

  /**
   * 获取性能指标
   */
  async getPerformanceMetrics(): Promise<{
    inferenceTime: string;
    memoryUsage: string;
    fps: string;
    totalInferences: string;
  }> {
    return {
      inferenceTime: await this.getText(this.selectors.inferenceTime),
      memoryUsage: await this.getText(this.selectors.memoryUsage),
      fps: await this.getText(this.selectors.fps),
      totalInferences: await this.getText(this.selectors.totalInferences)
    };
  }

  /**
   * 检查输出canvas是否有结果
   */
  async hasOutputResult(): Promise<boolean> {
    await this.waitForVisible(this.selectors.outputCanvas);

    return await this.page.evaluate(() => {
      const canvas = document.querySelector('#output-canvas') as HTMLCanvasElement;
      if (!canvas) return false;

      const ctx = canvas.getContext('2d');
      if (!ctx) return false;

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // 检查是否有非透明像素
      for (let i = 3; i < data.length; i += 4) {
        if (data[i] > 0) return true;
      }
      return false;
    });
  }

  /**
   * 获取输出canvas数据
   */
  async getOutputCanvasData(): Promise<ImageData> {
    await this.waitForVisible(this.selectors.outputCanvas);

    return await this.page.evaluate(() => {
      const canvas = document.querySelector('#output-canvas') as HTMLCanvasElement;
      const ctx = canvas.getContext('2d')!;
      return ctx.getImageData(0, 0, canvas.width, canvas.height);
    });
  }

  /**
   * 检查渲染结果是否全黑或全白
   */
  async checkRenderResult(): Promise<{ isAllBlack: boolean; isAllWhite: boolean; }> {
    await this.waitForVisible(this.selectors.outputCanvas);

    return await this.page.evaluate(() => {
      const canvas = document.querySelector('#output-canvas') as HTMLCanvasElement;
      const ctx = canvas.getContext('2d')!;
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      let allBlack = true;
      let allWhite = true;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];

        // 只检查非透明像素
        if (a > 0) {
          // 检查是否为黑色 (接近0)
          if (r > 5 || g > 5 || b > 5) {
            allBlack = false;
          }

          // 检查是否为白色 (接近255)
          if (r < 250 || g < 250 || b < 250) {
            allWhite = false;
          }

          // 如果既不是全黑也不是全白，可以提前退出
          if (!allBlack && !allWhite) {
            break;
          }
        }
      }

      return { isAllBlack: allBlack, isAllWhite: allWhite };
    });
  }

  /**
   * 清除推理结果
   */
  async clearResults(): Promise<void> {
    await this.safeClick(this.selectors.clearResultsBtn);
    await this.page.waitForTimeout(500);
  }

  /**
   * 检查推理按钮是否可用
   */
  async isInferenceButtonEnabled(): Promise<boolean> {
    const button = this.page.locator(this.selectors.inferenceBtn);
    return await button.isEnabled();
  }

  /**
   * 检查基准测试按钮是否可用
   */
  async isBenchmarkButtonEnabled(): Promise<boolean> {
    const button = this.page.locator(this.selectors.benchmarkBtn);
    return await button.isEnabled();
  }

  /**
   * 等待推理准备就绪
   */
  async waitForInferenceReady(): Promise<void> {
    // 等待模型和图片都加载完成，推理按钮可用
    await this.page.waitForFunction(() => {
      const modelStatus = document.querySelector('#model-status')?.textContent;
      const inferenceBtn = document.querySelector('#inference-btn') as HTMLButtonElement;

      return modelStatus?.includes('成功') && !inferenceBtn?.disabled;
    }, { timeout: 30000 });
  }

  /**
   * 获取WebGL上下文信息
   */
  async getWebGLInfo(): Promise<any> {
    return await this.page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

      if (!gl) {
        return { supported: false };
      }

      return {
        supported: true,
        version: gl.getParameter(gl.VERSION),
        renderer: gl.getParameter(gl.RENDERER),
        vendor: gl.getParameter(gl.VENDOR),
        maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
        maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
        extensions: gl.getSupportedExtensions()
      };
    });
  }

  /**
   * 监控内存使用情况
   */
  async getMemoryInfo(): Promise<any> {
    return await this.page.evaluate(() => {
      if ('memory' in performance) {
        return {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        };
      }
      return null;
    });
  }
}