import { test as base, expect } from '@playwright/test';
import { InferencePage } from './InferencePage';
import { ImageUtils, PerformanceUtils, WaitUtils, ValidationUtils } from './TestUtils';
import path from 'path';

/**
 * 扩展的测试上下文
 * 提供预配置的页面对象和工具方法
 */
export const test = base.extend<{
  inferencePage: InferencePage;
  imageUtils: typeof ImageUtils;
  performanceUtils: typeof PerformanceUtils;
  waitUtils: typeof WaitUtils;
  validationUtils: typeof ValidationUtils;
}>({
  // 预配置的推理页面对象
  inferencePage: async ({ page }, use) => {
    const inferencePage = new InferencePage(page);
    await inferencePage.goto();
    await use(inferencePage);
  },

  // 图像处理工具
  imageUtils: async ({}, use) => {
    await use(ImageUtils);
  },

  // 性能监控工具
  performanceUtils: async ({}, use) => {
    await use(PerformanceUtils);
  },

  // 等待工具
  waitUtils: async ({}, use) => {
    await use(WaitUtils);
  },

  // 验证工具
  validationUtils: async ({}, use) => {
    await use(ValidationUtils);
  },
});

/**
 * 测试数据常量
 */
export const TEST_CONSTANTS = {
  // 测试图片路径
  TEST_IMAGES: {
    STANDARD: path.join(process.cwd(), 'tests/assets/test.png'),
    SMALL: path.join(process.cwd(), 'tests/assets/test_224.png'),
    LARGE: path.join(process.cwd(), 'tests/assets/large_1024.png'),
    GRAYSCALE: path.join(process.cwd(), 'tests/assets/test_gray.png'),
    EDGE_CASE: path.join(process.cwd(), 'tests/assets/edge_1x1.png'),
    SOLID_RED: path.join(process.cwd(), 'tests/assets/solid_red.png'),
    SOLID_GREEN: path.join(process.cwd(), 'tests/assets/solid_green.png'),
    SOLID_BLUE: path.join(process.cwd(), 'tests/assets/solid_blue.png'),
    TRANSPARENT: path.join(process.cwd(), 'tests/assets/test_transparent.png')
  },

  // 模型文件路径
  MODEL_PATHS: {
    DEFAULT: '/model.onnx',
    SIMPLE: '/simple_add.onnx'
  },

  // 超时时间
  TIMEOUTS: {
    MODEL_LOAD: 30000,
    INFERENCE: 60000,
    BENCHMARK: 120000,
    WEBGL_CHECK: 5000,
    PAGE_LOAD: 30000
  },

  // 性能阈值
  PERFORMANCE_THRESHOLDS: {
    MAX_INFERENCE_TIME: 10000, // 10秒
    MAX_MODEL_LOAD_TIME: 30000, // 30秒
    MAX_MEMORY_GROWTH: 100 * 1024 * 1024, // 100MB
    MIN_FPS: 0.1, // 最小FPS
    MAX_PIXEL_DIFFERENCE: 5 // 像素差异百分比
  },

  // 期望的输出特征
  EXPECTED_OUTPUT: {
    MIN_WIDTH: 1,
    MIN_HEIGHT: 1,
    MAX_WIDTH: 2048,
    MAX_HEIGHT: 2048,
    PIXEL_VALUE_MIN: 0,
    PIXEL_VALUE_MAX: 255
  }
};

/**
 * 测试辅助函数
 */
export class TestHelpers {
  /**
   * 准备完整的推理环境
   */
  static async prepareInferenceEnvironment(
    inferencePage: InferencePage,
    modelType: 'default' | 'simple' = 'default',
    imageType: keyof typeof TEST_CONSTANTS.TEST_IMAGES = 'STANDARD'
  ): Promise<void> {
    // 加载模型
    if (modelType === 'default') {
      await inferencePage.loadDefaultModel();
    } else {
      await inferencePage.loadSimpleModel();
    }

    // 验证模型加载
    expect(await inferencePage.isModelLoaded(), '模型应该加载成功').toBe(true);

    // 加载图片
    if (imageType === 'STANDARD') {
      await inferencePage.loadDefaultImage();
    } else {
      const imagePath = TEST_CONSTANTS.TEST_IMAGES[imageType];
      await inferencePage.uploadImageFile(imagePath);
    }

    // 验证图片加载
    expect(await inferencePage.isImageLoaded(), '图片应该加载成功').toBe(true);

    // 等待推理准备就绪
    await inferencePage.waitForInferenceReady();
  }

  /**
   * 执行标准推理测试
   */
  static async performStandardInference(
    inferencePage: InferencePage
  ): Promise<{
    executionTime: number;
    outputData: ImageData;
    metrics: any;
  }> {
    const startTime = Date.now();
    
    // 执行推理
    await inferencePage.runInference();
    
    const endTime = Date.now();
    const executionTime = endTime - startTime;

    // 验证推理完成
    expect(await inferencePage.isInferenceComplete(), '推理应该完成').toBe(true);

    // 获取输出数据
    const outputData = await inferencePage.getOutputCanvasData();
    expect(outputData, '应该有输出数据').toBeTruthy();

    // 获取性能指标
    const metrics = await inferencePage.getPerformanceMetrics();

    return { executionTime, outputData, metrics };
  }

  /**
   * 验证WebGL环境
   */
  static async validateWebGLEnvironment(inferencePage: InferencePage): Promise<void> {
    const webglInfo = await inferencePage.getWebGLInfo();
    
    expect(webglInfo.supported, 'WebGL应该被支持').toBe(true);
    expect(webglInfo.maxTextureSize, 'WebGL纹理尺寸应该足够').toBeGreaterThanOrEqual(512);
    expect(webglInfo.version, 'WebGL版本应该存在').toBeTruthy();
    expect(webglInfo.renderer, 'WebGL渲染器信息应该存在').toBeTruthy();
  }

  /**
   * 检查系统资源状况
   */
  static async checkSystemResources(inferencePage: InferencePage): Promise<{
    memory: any;
    webgl: any;
    warnings: string[];
  }> {
    const warnings: string[] = [];
    
    // 检查内存
    const memory = await inferencePage.getMemoryInfo();
    if (!memory) {
      warnings.push('内存监控API不可用');
    } else if (memory.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
      warnings.push('内存使用量较高');
    }

    // 检查WebGL
    const webgl = await inferencePage.getWebGLInfo();
    if (!webgl.supported) {
      warnings.push('WebGL不支持');
    } else {
      if (webgl.maxTextureSize < 1024) {
        warnings.push('WebGL纹理尺寸限制较小');
      }
    }

    return { memory, webgl, warnings };
  }

  /**
   * 生成测试报告数据
   */
  static generateTestReport(testResults: any[]): {
    summary: any;
    details: any[];
    recommendations: string[];
  } {
    const summary = {
      totalTests: testResults.length,
      passedTests: testResults.filter(r => r.status === 'passed').length,
      failedTests: testResults.filter(r => r.status === 'failed').length,
      averageExecutionTime: 0,
      totalExecutionTime: 0
    };

    let totalTime = 0;
    const details = testResults.map(result => {
      const executionTime = result.executionTime || 0;
      totalTime += executionTime;
      
      return {
        testName: result.testName,
        status: result.status,
        executionTime,
        error: result.error,
        warnings: result.warnings || []
      };
    });

    summary.totalExecutionTime = totalTime;
    summary.averageExecutionTime = testResults.length > 0 ? totalTime / testResults.length : 0;

    // 生成建议
    const recommendations: string[] = [];
    
    if (summary.failedTests > 0) {
      recommendations.push('存在失败的测试用例，需要检查具体错误信息');
    }
    
    if (summary.averageExecutionTime > 5000) {
      recommendations.push('平均执行时间较长，建议优化性能');
    }

    return { summary, details, recommendations };
  }

  /**
   * 清理测试环境
   */
  static async cleanupTestEnvironment(inferencePage: InferencePage): Promise<void> {
    try {
      // 清除推理结果
      await inferencePage.clearResults();
      
      // 等待一段时间让资源释放
      await inferencePage.page.waitForTimeout(1000);
      
      // 强制垃圾回收（如果支持）
      await inferencePage.page.evaluate(() => {
        if ('gc' in window) {
          (window as any).gc();
        }
      });
    } catch (error) {
      console.warn('清理测试环境时出现错误:', error);
    }
  }
}

/**
 * 自定义匹配器
 */
export const customMatchers = {
  /**
   * 验证推理输出格式
   */
  toBeValidInferenceOutput(received: ImageData) {
    const pass = received && 
                 received.data && 
                 received.width > 0 && 
                 received.height > 0 &&
                 received.data.length === received.width * received.height * 4;

    return {
      message: () => pass 
        ? `期望不是有效的推理输出` 
        : `期望是有效的推理输出，但收到的数据结构不正确`,
      pass,
    };
  },

  /**
   * 验证性能指标在合理范围内
   */
  toBeWithinPerformanceThreshold(received: number, threshold: number) {
    const pass = received >= 0 && received <= threshold;

    return {
      message: () => pass
        ? `期望性能指标 ${received} 不在阈值 ${threshold} 内`
        : `期望性能指标 ${received} 在阈值 ${threshold} 内`,
      pass,
    };
  }
};

// 导出自定义expect
export { expect };

/**
 * 测试套件组织工具
 */
export class TestSuiteOrganizer {
  /**
   * 创建冒烟测试套件
   */
  static createSmokeTests() {
    return [
      'WebGL2支持检查',
      '默认ONNX模型加载',
      '完整推理流程测试'
    ];
  }

  /**
   * 创建回归测试套件
   */
  static createRegressionTests() {
    return [
      'WebGL兼容性测试',
      'ONNX模型加载测试',
      '推理功能测试',
      '结果验证测试',
      '错误处理测试'
    ];
  }

  /**
   * 创建性能测试套件
   */
  static createPerformanceTests() {
    return [
      '推理性能基准测试',
      '内存性能监控',
      '批量推理吞吐量测试',
      '不同尺寸图片性能比较'
    ];
  }
}