import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';

/**
 * 关键推理测试 - 验证public/model.onnx的基本功能
 */
test.describe('关键推理验证', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('验证模型文件可访问', async () => {
    console.log('🔍 检查模型文件可访问性...');
    
    // 直接检查模型文件是否可访问
    const modelAccessible = await inferencePage.page.evaluate(async () => {
      try {
        const response = await fetch('/model.onnx');
        return {
          accessible: response.ok,
          status: response.status,
          contentLength: response.headers.get('content-length')
        };
      } catch (error) {
        return {
          accessible: false,
          error: error.toString()
        };
      }
    });
    
    console.log('模型文件访问结果:', modelAccessible);
    expect(modelAccessible.accessible, '模型文件应该可以访问').toBe(true);
    expect(parseInt(modelAccessible.contentLength || '0'), '模型文件应该有内容').toBeGreaterThan(0);
  });

  test('验证页面基本功能', async () => {
    console.log('🔍 检查页面基本元素...');
    
    // 检查关键页面元素是否存在
    const elements = await inferencePage.page.evaluate(() => {
      return {
        loadDefaultModelBtn: !!document.querySelector('button:has-text("加载默认模型")'),
        modelStatus: !!document.querySelector('#model-status'),
        inferenceBtn: !!document.querySelector('#inference-btn'),
        inputCanvas: !!document.querySelector('#input-canvas'),
        outputCanvas: !!document.querySelector('#output-canvas')
      };
    });
    
    console.log('页面元素检查:', elements);
    expect(elements.loadDefaultModelBtn, '加载默认模型按钮应该存在').toBe(true);
    expect(elements.modelStatus, '模型状态元素应该存在').toBe(true);
    expect(elements.inputCanvas, '输入画布应该存在').toBe(true);
    expect(elements.outputCanvas, '输出画布应该存在').toBe(true);
  });

  test('尝试加载默认模型', async () => {
    console.log('📦 尝试加载默认模型...');
    
    // 点击加载默认模型按钮
    await inferencePage.page.click('button:has-text("加载默认模型")');
    
    // 等待一段时间让模型加载
    await inferencePage.page.waitForTimeout(10000);
    
    // 检查模型状态
    const modelStatus = await inferencePage.getModelStatus();
    console.log('模型加载状态:', modelStatus);
    
    // 验证状态不是初始状态
    expect(modelStatus, '模型状态应该有变化').not.toContain('请选择或加载ONNX模型文件');
  });

  test('检查WebGL环境', async () => {
    console.log('🎮 检查WebGL环境...');
    
    const webglInfo = await inferencePage.getWebGLInfo();
    console.log('WebGL信息:', webglInfo);
    
    expect(webglInfo.supported, 'WebGL应该被支持').toBe(true);
    expect(webglInfo.maxTextureSize, 'WebGL纹理尺寸应该足够').toBeGreaterThanOrEqual(512);
  });

  test('尝试完整流程', async () => {
    console.log('🚀 尝试完整推理流程...');
    
    try {
      // 1. 加载模型
      console.log('步骤1: 加载模型');
      await inferencePage.page.click('button:has-text("加载默认模型")');
      await inferencePage.page.waitForTimeout(15000); // 给更长时间加载模型
      
      const modelStatus = await inferencePage.getModelStatus();
      console.log('模型状态:', modelStatus);
      
      // 2. 加载图片
      console.log('步骤2: 加载默认图片');
      await inferencePage.page.click('button:has-text("加载默认图片")');
      await inferencePage.page.waitForTimeout(3000);
      
      // 3. 检查推理按钮状态
      const buttonEnabled = await inferencePage.isInferenceButtonEnabled();
      console.log('推理按钮是否可用:', buttonEnabled);
      
      if (buttonEnabled) {
        // 4. 尝试执行推理
        console.log('步骤3: 执行推理');
        await inferencePage.page.click('#inference-btn');
        await inferencePage.page.waitForTimeout(20000); // 给更长时间执行推理
        
        const inferenceStatus = await inferencePage.getInferenceStatus();
        console.log('推理状态:', inferenceStatus);
        
        // 5. 检查是否有输出
        const hasOutput = await inferencePage.hasOutputResult();
        console.log('是否有推理输出:', hasOutput);
      } else {
        console.log('推理按钮未启用，可能模型加载失败');
      }
      
    } catch (error) {
      console.log('完整流程执行中出现错误:', error);
      
      // 即使出错也记录当前状态
      const currentModelStatus = await inferencePage.getModelStatus();
      const currentInferenceStatus = await inferencePage.getInferenceStatus();
      console.log('错误时模型状态:', currentModelStatus);
      console.log('错误时推理状态:', currentInferenceStatus);
    }
  });
});