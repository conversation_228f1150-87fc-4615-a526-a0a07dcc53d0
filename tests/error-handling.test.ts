import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';
import path from 'path';

/**
 * 错误处理测试套件
 * 验证系统在各种异常情况下的处理能力
 */
test.describe('错误处理测试', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('模型文件不存在错误处理', async () => {
    // 模拟模型文件请求失败
    await inferencePage.page.route('/model.onnx', route => {
      route.abort('failed');
    });
    
    // 尝试加载默认模型
    await inferencePage.loadDefaultModel();
    
    // 验证错误状态显示
    const modelStatus = await inferencePage.getModelStatus();
    const statusLower = modelStatus.toLowerCase();
    
    const hasErrorInfo = statusLower.includes('失败') || 
                        statusLower.includes('错误') || 
                        statusLower.includes('error') ||
                        statusLower.includes('failed');
    
    expect(hasErrorInfo, '应该显示错误信息').toBe(true);
    
    // 验证模型未加载
    expect(await inferencePage.isModelLoaded(), '模型不应该加载成功').toBe(false);
    
    console.log('模型加载失败状态:', modelStatus);
  });

  test('网络错误处理', async () => {
    // 模拟网络完全断开
    await inferencePage.page.route('**/*', route => {
      // 只拦截模型文件请求
      if (route.request().url().includes('.onnx')) {
        route.abort('internetdisconnected');
      } else {
        route.continue();
      }
    });
    
    // 尝试加载模型
    await inferencePage.loadDefaultModel();
    
    // 验证网络错误处理
    const modelStatus = await inferencePage.getModelStatus();
    console.log('网络错误状态:', modelStatus);
    
    // 应该显示相关错误信息
    expect(await inferencePage.isModelLoaded(), '网络错误时模型不应该加载').toBe(false);
  });

  test('无效ONNX文件错误处理', async () => {
    // 模拟返回无效的ONNX文件内容
    await inferencePage.page.route('/model.onnx', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/octet-stream',
        body: 'invalid onnx file content'
      });
    });
    
    // 尝试加载模型
    await inferencePage.loadDefaultModel();
    
    // 验证错误处理
    const modelStatus = await inferencePage.getModelStatus();
    console.log('无效文件错误状态:', modelStatus);
    
    expect(await inferencePage.isModelLoaded(), '无效文件不应该加载成功').toBe(false);
  });

  test('WebGL不支持错误处理', async () => {
    // 禁用WebGL
    await inferencePage.page.addInitScript(() => {
      // 覆盖getContext方法使其返回null
      const originalGetContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function(contextType: string, ...args: any[]) {
        if (contextType === 'webgl' || contextType === 'webgl2') {
          return null;
        }
        return originalGetContext.call(this, contextType, ...args);
      };
    });
    
    // 重新加载页面以应用初始化脚本
    await inferencePage.goto();
    
    // 检查WebGL状态
    const webglStatus = await inferencePage.checkWebGLSupport();
    console.log('WebGL禁用状态:', webglStatus);
    
    // 应该检测到WebGL不支持
    const statusLower = webglStatus.toLowerCase();
    const hasUnsupportedInfo = statusLower.includes('不支持') || 
                              statusLower.includes('unsupported') ||
                              statusLower.includes('disabled');
    
    if (hasUnsupportedInfo) {
      console.log('正确检测到WebGL不支持');
    }
    
    // 尝试加载模型（可能失败或有警告）
    await inferencePage.loadDefaultModel();
    const modelStatus = await inferencePage.getModelStatus();
    console.log('WebGL不支持时模型状态:', modelStatus);
  });

  test('内存不足错误处理', async () => {
    // 这个测试比较难模拟，我们通过快速连续操作来测试内存压力
    await inferencePage.loadDefaultModel();
    expect(await inferencePage.isModelLoaded()).toBe(true);
    
    // 加载大图片
    const largeImagePath = path.join(process.cwd(), 'tests/assets/large_1024.png');
    await inferencePage.uploadImageFile(largeImagePath);
    
    // 连续执行多次推理来测试内存处理
    let errorOccurred = false;
    let consecutiveInferences = 0;
    
    try {
      for (let i = 0; i < 20; i++) {
        await inferencePage.waitForInferenceReady();
        await inferencePage.runInference();
        consecutiveInferences++;
        
        // 检查是否有错误状态
        const status = await inferencePage.getInferenceStatus();
        if (status.includes('失败') || status.includes('错误')) {
          errorOccurred = true;
          console.log(`第${i + 1}次推理出现错误:`, status);
          break;
        }
        
        // 短暂等待
        await inferencePage.page.waitForTimeout(100);
      }
    } catch (error) {
      errorOccurred = true;
      console.log('内存压力测试中捕获错误:', error);
    }
    
    console.log(`完成连续推理次数: ${consecutiveInferences}`);
    
    // 无论是否出现错误，系统都应该能处理
    expect(consecutiveInferences, '应该至少完成几次推理').toBeGreaterThan(0);
  });

  test('无图片推理错误处理', async () => {
    // 只加载模型，不加载图片
    await inferencePage.loadDefaultModel();
    expect(await inferencePage.isModelLoaded()).toBe(true);
    
    // 检查推理按钮状态（应该不可用）
    const isButtonEnabled = await inferencePage.isInferenceButtonEnabled();
    console.log('无图片时推理按钮状态:', isButtonEnabled);
    
    // 推理按钮应该不可用或者点击后显示错误
    if (isButtonEnabled) {
      // 如果按钮可用，尝试点击并检查错误处理
      await inferencePage.runInference();
      const status = await inferencePage.getInferenceStatus();
      console.log('无图片推理状态:', status);
      
      // 应该有相应的错误提示
      const hasErrorInfo = status.includes('失败') || 
                          status.includes('错误') || 
                          status.includes('图片') ||
                          status.includes('image');
      
      expect(hasErrorInfo, '应该提示需要图片').toBe(true);
    } else {
      console.log('推理按钮正确禁用');
    }
  });

  test('无模型推理错误处理', async () => {
    // 只加载图片，不加载模型
    await inferencePage.loadDefaultImage();
    expect(await inferencePage.isImageLoaded()).toBe(true);
    
    // 检查推理按钮状态
    const isButtonEnabled = await inferencePage.isInferenceButtonEnabled();
    console.log('无模型时推理按钮状态:', isButtonEnabled);
    
    // 推理按钮应该不可用
    expect(isButtonEnabled, '无模型时推理按钮应该禁用').toBe(false);
  });

  test('图片格式错误处理', async () => {
    // 创建一个无效的图片文件路径
    const invalidImagePath = path.join(process.cwd(), 'tests/assets/invalid.txt');
    
    // 创建一个文本文件作为无效图片
    const fs = require('fs');
    if (!fs.existsSync(path.dirname(invalidImagePath))) {
      fs.mkdirSync(path.dirname(invalidImagePath), { recursive: true });
    }
    fs.writeFileSync(invalidImagePath, 'This is not an image file');
    
    try {
      // 尝试上传无效图片
      await inferencePage.uploadImageFile(invalidImagePath);
      
      // 检查图片加载状态
      const imageLoaded = await inferencePage.isImageLoaded();
      console.log('无效图片加载状态:', imageLoaded);
      
      // 无效图片不应该加载成功
      expect(imageLoaded, '无效图片不应该加载成功').toBe(false);
      
    } catch (error) {
      console.log('无效图片上传错误:', error);
      // 这也是正确的错误处理
    } finally {
      // 清理临时文件
      if (fs.existsSync(invalidImagePath)) {
        fs.unlinkSync(invalidImagePath);
      }
    }
  });

  test('推理超时错误处理', async () => {
    // 准备正常的推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 设置较短的超时时间来测试超时处理
    const shortTimeout = 100; // 100ms
    
    try {
      // 使用短超时执行推理
      await Promise.race([
        inferencePage.runInference(),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('推理超时')), shortTimeout);
        })
      ]);
      
      console.log('推理在超时前完成');
      
    } catch (error) {
      console.log('捕获推理超时:', error);
      
      // 检查页面状态
      const status = await inferencePage.getInferenceStatus();
      console.log('超时后推理状态:', status);
    }
  });

  test('浏览器兼容性错误处理', async () => {
    // 检查当前浏览器的兼容性信息
    const webglInfo = await inferencePage.getWebGLInfo();
    const memoryInfo = await inferencePage.getMemoryInfo();
    
    console.log('浏览器兼容性信息:');
    console.log('- WebGL支持:', webglInfo.supported);
    console.log('- WebGL版本:', webglInfo.version);
    console.log('- 内存API支持:', memoryInfo !== null);
    
    // 基本兼容性检查
    expect(webglInfo.supported, '当前测试环境应该支持WebGL').toBe(true);
    
    // 检查关键特性
    if (webglInfo.extensions) {
      const hasFloatTextures = webglInfo.extensions.includes('OES_texture_float') ||
                              webglInfo.extensions.includes('EXT_color_buffer_float');
      
      console.log('- 浮点纹理支持:', hasFloatTextures);
      
      if (!hasFloatTextures) {
        console.warn('警告: 当前环境可能不支持浮点纹理');
      }
    }
  });

  test('页面刷新后状态恢复', async () => {
    // 加载模型和图片
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 执行一次推理
    await inferencePage.runInference();
    expect(await inferencePage.isInferenceComplete()).toBe(true);
    
    // 刷新页面
    await inferencePage.page.reload();
    await inferencePage.waitForLoad();
    
    // 检查页面状态重置
    const modelStatus = await inferencePage.getModelStatus();
    const inferenceStatus = await inferencePage.getInferenceStatus();
    
    console.log('刷新后模型状态:', modelStatus);
    console.log('刷新后推理状态:', inferenceStatus);
    
    // 状态应该重置到初始状态
    expect(await inferencePage.isModelLoaded(), '刷新后模型应该未加载').toBe(false);
    expect(await inferencePage.isInferenceButtonEnabled(), '刷新后推理按钮应该禁用').toBe(false);
  });

  test('并发推理错误处理', async () => {
    // 准备推理
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    
    // 尝试同时执行多个推理（应该被串行化或产生错误）
    const concurrentInferences = [
      inferencePage.runInference(),
      inferencePage.runInference(),
      inferencePage.runInference()
    ];
    
    try {
      await Promise.all(concurrentInferences);
      console.log('所有并发推理完成');
      
    } catch (error) {
      console.log('并发推理错误:', error);
    }
    
    // 检查最终状态
    const finalStatus = await inferencePage.getInferenceStatus();
    console.log('并发推理后状态:', finalStatus);
    
    // 应该有明确的状态
    expect(finalStatus.length, '应该有状态信息').toBeGreaterThan(0);
  });

  test('资源清理验证', async () => {
    // 获取初始内存
    const initialMemory = await inferencePage.getMemoryInfo();
    
    // 执行完整流程
    await inferencePage.loadDefaultModel();
    await inferencePage.loadDefaultImage();
    await inferencePage.runInference();
    
    // 清理结果
    await inferencePage.clearResults();
    
    // 强制垃圾回收（如果支持）
    await inferencePage.page.evaluate(() => {
      if ('gc' in window) {
        (window as any).gc();
      }
    });
    
    // 等待一段时间让垃圾回收执行
    await inferencePage.page.waitForTimeout(2000);
    
    // 检查内存变化
    const finalMemory = await inferencePage.getMemoryInfo();
    
    if (initialMemory && finalMemory) {
      const memoryDiff = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      console.log(`内存变化: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);
      
      // 内存增长应该在合理范围内
      expect(Math.abs(memoryDiff), '资源清理后内存变化应该合理').toBeLessThan(50 * 1024 * 1024); // 50MB
    }
    
    console.log('资源清理验证完成');
  });
});