import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';
import { TestHelpers, TEST_CONSTANTS } from './utils/TestFixtures';

/**
 * 完整的WebGL ONNX推理测试套件
 * 这是一个集成测试，验证整个推理流程的正确性
 * 
 * @smoke - 包含在冒烟测试中
 * @regression - 包含在回归测试中
 */
test.describe('WebGL ONNX推理完整测试套件', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test.afterEach(async () => {
    await TestHelpers.cleanupTestEnvironment(inferencePage);
  });

  test('🔥 完整推理流程验证 @smoke @regression', async () => {
    console.log('🎯 开始完整推理流程测试...');
    
    // 1. 环境检查
    console.log('📋 检查WebGL环境...');
    await TestHelpers.validateWebGLEnvironment(inferencePage);
    
    // 2. 准备推理环境
    console.log('⚙️ 准备推理环境...');
    await TestHelpers.prepareInferenceEnvironment(inferencePage, 'default', 'STANDARD');
    
    // 3. 执行推理
    console.log('🚀 执行推理...');
    const result = await TestHelpers.performStandardInference(inferencePage);
    
    // 4. 验证结果
    console.log('✅ 验证推理结果...');
    
    // 验证执行时间
    expect(result.executionTime, '推理时间应该在合理范围内')
      .toBeLessThan(TEST_CONSTANTS.PERFORMANCE_THRESHOLDS.MAX_INFERENCE_TIME);
    
    // 验证输出数据结构
    expect(result.outputData.width, '输出宽度应该大于0').toBeGreaterThan(0);
    expect(result.outputData.height, '输出高度应该大于0').toBeGreaterThan(0);
    expect(result.outputData.data.length, '输出数据长度应该正确')
      .toBe(result.outputData.width * result.outputData.height * 4);
    
    // 验证像素值范围
    for (let i = 0; i < result.outputData.data.length; i++) {
      const pixelValue = result.outputData.data[i];
      expect(pixelValue, `像素值应该在0-255范围内: index ${i}`)
        .toBeGreaterThanOrEqual(0);
      expect(pixelValue, `像素值应该在0-255范围内: index ${i}`)
        .toBeLessThanOrEqual(255);
    }
    
    // 验证有实际输出内容
    let hasNonTransparentPixels = false;
    for (let i = 3; i < result.outputData.data.length; i += 4) {
      if (result.outputData.data[i] > 0) {
        hasNonTransparentPixels = true;
        break;
      }
    }
    expect(hasNonTransparentPixels, '输出应该包含非透明像素').toBe(true);
    
    console.log('✅ 完整推理流程测试通过!');
    console.log(`📊 推理时间: ${result.executionTime}ms`);
    console.log(`📐 输出尺寸: ${result.outputData.width}x${result.outputData.height}`);
  });

  test('🎯 public/model.onnx 模型专项测试 @regression', async () => {
    console.log('🎯 开始 public/model.onnx 专项测试...');
    
    // 1. 验证模型文件可访问性
    console.log('📁 检查模型文件可访问性...');
    const modelAccessibility = await inferencePage.page.evaluate(async () => {
      try {
        const response = await fetch('/model.onnx');
        return {
          accessible: response.ok,
          status: response.status,
          size: parseInt(response.headers.get('content-length') || '0')
        };
      } catch (error) {
        return {
          accessible: false,
          error: error.toString()
        };
      }
    });
    
    expect(modelAccessibility.accessible, 'public/model.onnx 应该可访问').toBe(true);
    expect(modelAccessibility.size, '模型文件应该有内容').toBeGreaterThan(0);
    
    console.log(`📁 模型文件大小: ${(modelAccessibility.size / 1024).toFixed(2)} KB`);
    
    // 2. 加载 public/model.onnx
    console.log('📦 加载 public/model.onnx...');
    await inferencePage.loadDefaultModel();
    
    // 验证模型加载成功
    const modelStatus = await inferencePage.getModelStatus();
    expect(modelStatus, 'public/model.onnx 应该加载成功').toContain('成功');
    expect(await inferencePage.isModelLoaded(), '模型加载标记应该为true').toBe(true);
    
    console.log(`📦 模型状态: ${modelStatus}`);
    
    // 3. 获取模型信息
    const modelInfo = await inferencePage.getModelInfo();
    if (modelInfo) {
      console.log(`📋 模型信息: ${modelInfo}`);
    }
    
    // 4. 使用多种图片测试推理
    const testImages = [
      { name: '标准测试图', path: TEST_CONSTANTS.TEST_IMAGES.STANDARD },
      { name: '224x224图', path: TEST_CONSTANTS.TEST_IMAGES.SMALL },
      { name: '灰度图', path: TEST_CONSTANTS.TEST_IMAGES.GRAYSCALE }
    ];
    
    for (const img of testImages) {
      console.log(`🖼️ 测试图片: ${img.name}`);
      
      // 上传图片
      await inferencePage.uploadImageFile(img.path);
      expect(await inferencePage.isImageLoaded(), `${img.name} 应该加载成功`).toBe(true);
      
      // 等待推理准备就绪
      await inferencePage.waitForInferenceReady();
      
      // 执行推理
      const startTime = Date.now();
      await inferencePage.runInference();
      const endTime = Date.now();
      
      // 验证推理成功
      expect(await inferencePage.isInferenceComplete(), `${img.name} 推理应该完成`).toBe(true);
      expect(await inferencePage.hasOutputResult(), `${img.name} 应该有推理输出`).toBe(true);
      
      const inferenceTime = endTime - startTime;
      console.log(`⏱️ ${img.name} 推理时间: ${inferenceTime}ms`);
      
      // 获取输出数据
      const outputData = await inferencePage.getOutputCanvasData();
      console.log(`📐 ${img.name} 输出尺寸: ${outputData.width}x${outputData.height}`);
      
      // 清除结果
      await inferencePage.clearResults();
    }
    
    console.log('✅ public/model.onnx 专项测试完成!');
  });

  test('⚡ 性能基准测试 @performance', async () => {
    console.log('⚡ 开始性能基准测试...');
    
    // 准备测试环境
    await TestHelpers.prepareInferenceEnvironment(inferencePage);
    
    // 预热推理（消除初始化开销）
    console.log('🔥 预热推理...');
    await inferencePage.runInference();
    await inferencePage.clearResults();
    
    // 执行基准测试
    console.log('📊 执行基准测试...');
    await inferencePage.runBenchmark();
    
    // 验证基准测试完成
    const status = await inferencePage.getInferenceStatus();
    expect(status, '基准测试应该完成').toContain('基准测试完成');
    
    // 获取性能指标
    const metrics = await inferencePage.getPerformanceMetrics();
    console.log('📊 性能指标:', metrics);
    
    // 验证性能指标
    const inferenceTime = parseFloat(metrics.inferenceTime);
    if (!isNaN(inferenceTime)) {
      expect(inferenceTime, '推理时间应该在合理范围内')
        .toBeLessThan(TEST_CONSTANTS.PERFORMANCE_THRESHOLDS.MAX_INFERENCE_TIME);
      console.log(`⏱️ 推理时间: ${inferenceTime}ms`);
    }
    
    const fps = parseFloat(metrics.fps);
    if (!isNaN(fps)) {
      expect(fps, 'FPS应该大于最小阈值')
        .toBeGreaterThan(TEST_CONSTANTS.PERFORMANCE_THRESHOLDS.MIN_FPS);
      console.log(`🎮 FPS: ${fps}`);
    }
    
    const totalInferences = parseInt(metrics.totalInferences);
    if (!isNaN(totalInferences)) {
      expect(totalInferences, '总推理次数应该增加').toBeGreaterThan(1);
      console.log(`🔢 总推理次数: ${totalInferences}`);
    }
    
    console.log('✅ 性能基准测试完成!');
  });

  test('🛡️ 错误恢复能力测试 @regression', async () => {
    console.log('🛡️ 开始错误恢复能力测试...');
    
    // 1. 测试网络错误恢复
    console.log('🌐 测试网络错误处理...');
    
    // 拦截模型请求，模拟网络错误
    await inferencePage.page.route('/public/model.onnx', route => {
      route.abort('failed');
    });
    
    // 尝试加载模型
    await inferencePage.loadDefaultModel();
    
    // 验证错误处理
    const errorStatus = await inferencePage.getModelStatus();
    const hasErrorInfo = errorStatus.includes('失败') || 
                        errorStatus.includes('错误') || 
                        errorStatus.includes('error');
    expect(hasErrorInfo, '应该显示错误信息').toBe(true);
    
    console.log(`🚨 错误状态: ${errorStatus}`);
    
    // 2. 恢复网络，重新测试
    console.log('🔄 恢复网络连接...');
    await inferencePage.page.unroute('/public/model.onnx');
    
    // 重新加载模型
    await inferencePage.loadDefaultModel();
    
    // 验证恢复成功
    const recoveryStatus = await inferencePage.getModelStatus();
    expect(recoveryStatus, '网络恢复后应该加载成功').toContain('成功');
    
    console.log(`✅ 恢复状态: ${recoveryStatus}`);
    
    // 3. 继续完成正常推理流程
    await inferencePage.loadDefaultImage();
    await inferencePage.waitForInferenceReady();
    await inferencePage.runInference();
    
    expect(await inferencePage.isInferenceComplete(), '恢复后推理应该成功').toBe(true);
    
    console.log('✅ 错误恢复能力测试完成!');
  });

  test('🔍 多浏览器兼容性检查 @regression', async () => {
    console.log('🔍 开始浏览器兼容性检查...');
    
    // 获取浏览器信息
    const browserInfo = await inferencePage.page.evaluate(() => ({
      userAgent: navigator.userAgent,
      vendor: navigator.vendor,
      platform: navigator.platform
    }));
    
    console.log('🌐 浏览器信息:', browserInfo);
    
    // 检查WebGL支持
    const webglInfo = await inferencePage.getWebGLInfo();
    console.log('🎮 WebGL信息:', webglInfo);
    
    expect(webglInfo.supported, '当前浏览器应该支持WebGL').toBe(true);
    
    // 检查关键WebGL特性
    const criticalFeatures = {
      maxTextureSize: webglInfo.maxTextureSize >= 512,
      hasRenderer: !!webglInfo.renderer,
      hasVersion: !!webglInfo.version,
      hasExtensions: Array.isArray(webglInfo.extensions) && webglInfo.extensions.length > 0
    };
    
    for (const [feature, supported] of Object.entries(criticalFeatures)) {
      expect(supported, `关键特性 ${feature} 应该支持`).toBe(true);
    }
    
    // 执行基本推理测试
    await TestHelpers.prepareInferenceEnvironment(inferencePage);
    const result = await TestHelpers.performStandardInference(inferencePage);
    
    expect(result.executionTime, '推理应该在合理时间内完成')
      .toBeLessThan(TEST_CONSTANTS.PERFORMANCE_THRESHOLDS.MAX_INFERENCE_TIME);
    
    console.log('✅ 浏览器兼容性检查完成!');
    console.log(`⏱️ 兼容性测试推理时间: ${result.executionTime}ms`);
  });

  test('📈 资源使用监控 @performance', async () => {
    console.log('📈 开始资源使用监控...');
    
    // 获取初始资源状态
    const initialResources = await TestHelpers.checkSystemResources(inferencePage);
    console.log('📊 初始资源状态:', initialResources);
    
    if (initialResources.warnings.length > 0) {
      console.warn('⚠️ 系统警告:', initialResources.warnings);
    }
    
    // 执行多次推理来监控资源使用
    await TestHelpers.prepareInferenceEnvironment(inferencePage);
    
    const inferenceCount = 5;
    const memorySnapshots = [];
    
    for (let i = 0; i < inferenceCount; i++) {
      console.log(`🔄 执行第 ${i + 1} 次推理...`);
      
      await inferencePage.runInference();
      
      // 记录内存使用
      const memory = await inferencePage.getMemoryInfo();
      if (memory) {
        memorySnapshots.push({
          iteration: i + 1,
          usedMemory: memory.usedJSHeapSize / 1024 / 1024 // MB
        });
      }
      
      await inferencePage.clearResults();
      await inferencePage.page.waitForTimeout(500); // 等待资源释放
    }
    
    // 分析内存使用趋势
    if (memorySnapshots.length > 1) {
      const firstMemory = memorySnapshots[0].usedMemory;
      const lastMemory = memorySnapshots[memorySnapshots.length - 1].usedMemory;
      const memoryGrowth = lastMemory - firstMemory;
      
      console.log('📊 内存使用情况:', memorySnapshots);
      console.log(`📈 内存增长: ${memoryGrowth.toFixed(2)}MB`);
      
      // 验证内存增长在合理范围内
      expect(memoryGrowth, '内存增长应该在合理范围内')
        .toBeLessThan(TEST_CONSTANTS.PERFORMANCE_THRESHOLDS.MAX_MEMORY_GROWTH / 1024 / 1024);
    }
    
    // 获取最终资源状态
    const finalResources = await TestHelpers.checkSystemResources(inferencePage);
    console.log('📊 最终资源状态:', finalResources);
    
    console.log('✅ 资源使用监控完成!');
  });
});