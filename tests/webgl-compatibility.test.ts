import { test, expect } from '@playwright/test';
import { InferencePage } from './utils/InferencePage';

/**
 * WebGL兼容性测试套件
 * 验证浏览器对WebGL和相关特性的支持
 */
test.describe('WebGL兼容性测试', () => {
  let inferencePage: InferencePage;

  test.beforeEach(async ({ page }) => {
    inferencePage = new InferencePage(page);
    await inferencePage.goto();
  });

  test('WebGL2支持检查', async () => {
    // 获取WebGL信息
    const webglInfo = await inferencePage.getWebGLInfo();
    
    // 验证WebGL支持
    expect(webglInfo.supported, 'WebGL应该被支持').toBe(true);
    
    // 验证基本WebGL属性
    expect(webglInfo.version, 'WebGL版本应该存在').toBeTruthy();
    expect(webglInfo.renderer, 'WebGL渲染器信息应该存在').toBeTruthy();
    expect(webglInfo.vendor, 'WebGL供应商信息应该存在').toBeTruthy();
    
    // 验证纹理尺寸限制（至少512x512）
    expect(webglInfo.maxTextureSize, 'WebGL纹理尺寸应该至少为512').toBeGreaterThanOrEqual(512);
    
    // 验证视窗尺寸
    expect(webglInfo.maxViewportDims, '视窗尺寸应该存在').toBeTruthy();
    expect(webglInfo.maxViewportDims.length, '视窗尺寸应该包含宽高').toBe(2);
    expect(webglInfo.maxViewportDims[0], '视窗宽度应该大于0').toBeGreaterThan(0);
    expect(webglInfo.maxViewportDims[1], '视窗高度应该大于0').toBeGreaterThan(0);
    
    console.log('WebGL信息:', JSON.stringify(webglInfo, null, 2));
  });

  test('WebGL扩展支持检查', async () => {
    const webglInfo = await inferencePage.getWebGLInfo();
    
    // 确保WebGL被支持
    expect(webglInfo.supported).toBe(true);
    
    // 检查关键扩展支持
    const extensions = webglInfo.extensions || [];
    const requiredExtensions = [
      'OES_texture_float',
      'OES_texture_half_float'
    ];
    
    const optionalExtensions = [
      'WEBGL_depth_texture',
      'OES_standard_derivatives',
      'EXT_shader_texture_lod'
    ];
    
    // 记录支持的扩展
    console.log('支持的WebGL扩展:', extensions);
    
    // 检查必需的扩展
    for (const ext of requiredExtensions) {
      const isSupported = extensions.includes(ext);
      if (!isSupported) {
        console.warn(`警告: 缺少关键扩展 ${ext}`);
      }
    }
    
    // 检查可选扩展
    for (const ext of optionalExtensions) {
      const isSupported = extensions.includes(ext);
      console.log(`可选扩展 ${ext}: ${isSupported ? '支持' : '不支持'}`);
    }
    
    // 至少应该支持一些基本扩展
    expect(extensions.length, '应该支持一些WebGL扩展').toBeGreaterThan(0);
  });

  test('页面WebGL状态显示检查', async () => {
    // 等待页面加载完成
    await inferencePage.waitForLoad();
    
    // 检查WebGL状态显示
    const webglStatus = await inferencePage.checkWebGLSupport();
    
    // 状态应该不为空
    expect(webglStatus, 'WebGL状态应该显示').toBeTruthy();
    
    // 状态应该包含相关信息
    const statusLower = webglStatus.toLowerCase();
    const hasWebglInfo = statusLower.includes('webgl') || 
                        statusLower.includes('支持') || 
                        statusLower.includes('不支持') ||
                        statusLower.includes('检查');
    
    expect(hasWebglInfo, 'WebGL状态应该包含相关信息').toBe(true);
    
    console.log('页面WebGL状态:', webglStatus);
  });

  test('系统兼容性重新检查功能', async () => {
    // 获取初始状态
    const initialStatus = await inferencePage.checkWebGLSupport();
    
    // 执行重新检查
    await inferencePage.recheckSystemCapabilities();
    
    // 获取检查后的状态
    const newStatus = await inferencePage.checkWebGLSupport();
    
    // 状态应该存在
    expect(newStatus, '重新检查后状态应该存在').toBeTruthy();
    
    console.log('初始状态:', initialStatus);
    console.log('重新检查后状态:', newStatus);
  });

  test('Canvas元素创建和WebGL上下文获取', async () => {
    // 测试是否能在页面中创建WebGL上下文
    const canCreateContext = await inferencePage.page.evaluate(() => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        return gl !== null;
      } catch (error) {
        console.error('WebGL上下文创建失败:', error);
        return false;
      }
    });
    
    expect(canCreateContext, '应该能够创建WebGL上下文').toBe(true);
  });

  test('WebGL着色器编译测试', async () => {
    // 测试基本着色器编译
    const shaderCompileResult = await inferencePage.page.evaluate(() => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        
        if (!gl) return { success: false, error: 'No WebGL context' };
        
        // 顶点着色器
        const vertexShaderSource = `
          attribute vec2 a_position;
          void main() {
            gl_Position = vec4(a_position, 0.0, 1.0);
          }
        `;
        
        // 片段着色器
        const fragmentShaderSource = `
          precision mediump float;
          void main() {
            gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
          }
        `;
        
        // 编译着色器
        const vertexShader = gl.createShader(gl.VERTEX_SHADER);
        if (!vertexShader) return { success: false, error: 'Failed to create vertex shader' };
        
        gl.shaderSource(vertexShader, vertexShaderSource);
        gl.compileShader(vertexShader);
        
        if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
          const error = gl.getShaderInfoLog(vertexShader);
          return { success: false, error: `Vertex shader compile error: ${error}` };
        }
        
        const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
        if (!fragmentShader) return { success: false, error: 'Failed to create fragment shader' };
        
        gl.shaderSource(fragmentShader, fragmentShaderSource);
        gl.compileShader(fragmentShader);
        
        if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
          const error = gl.getShaderInfoLog(fragmentShader);
          return { success: false, error: `Fragment shader compile error: ${error}` };
        }
        
        return { success: true };
        
      } catch (error) {
        return { success: false, error: error.toString() };
      }
    });
    
    expect(shaderCompileResult.success, `着色器编译应该成功: ${shaderCompileResult.error || ''}`).toBe(true);
    
    console.log('着色器编译结果:', shaderCompileResult);
  });

  test('WebGL纹理创建测试', async () => {
    // 测试纹理创建和操作
    const textureTest = await inferencePage.page.evaluate(() => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        
        if (!gl) return { success: false, error: 'No WebGL context' };
        
        // 创建纹理
        const texture = gl.createTexture();
        if (!texture) return { success: false, error: 'Failed to create texture' };
        
        gl.bindTexture(gl.TEXTURE_2D, texture);
        
        // 设置纹理参数
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
        
        // 创建测试数据
        const width = 256;
        const height = 256;
        const data = new Uint8Array(width * height * 4);
        
        // 填充测试数据
        for (let i = 0; i < data.length; i += 4) {
          data[i] = 255;     // R
          data[i + 1] = 0;   // G
          data[i + 2] = 0;   // B
          data[i + 3] = 255; // A
        }
        
        // 上传纹理数据
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, width, height, 0, gl.RGBA, gl.UNSIGNED_BYTE, data);
        
        // 检查错误
        const error = gl.getError();
        if (error !== gl.NO_ERROR) {
          return { success: false, error: `WebGL error: ${error}` };
        }
        
        return { success: true, textureSize: { width, height } };
        
      } catch (error) {
        return { success: false, error: error.toString() };
      }
    });
    
    expect(textureTest.success, `纹理创建应该成功: ${textureTest.error || ''}`).toBe(true);
    
    console.log('纹理测试结果:', textureTest);
  });

  test('内存信息检查', async () => {
    const memoryInfo = await inferencePage.getMemoryInfo();
    
    if (memoryInfo) {
      // 验证内存信息结构
      expect(memoryInfo.usedJSHeapSize, 'JS堆已使用大小应该大于0').toBeGreaterThan(0);
      expect(memoryInfo.totalJSHeapSize, 'JS堆总大小应该大于已使用大小').toBeGreaterThanOrEqual(memoryInfo.usedJSHeapSize);
      expect(memoryInfo.jsHeapSizeLimit, 'JS堆大小限制应该大于总大小').toBeGreaterThanOrEqual(memoryInfo.totalJSHeapSize);
      
      console.log('内存信息:', memoryInfo);
    } else {
      console.log('当前浏览器不支持性能内存API');
    }
  });
});