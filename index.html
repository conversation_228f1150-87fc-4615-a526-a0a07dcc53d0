<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebGL ONNX 推理库演示</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
      font-size: 2.5em;
      font-weight: 300;
    }

    .demo-section {
      margin-bottom: 40px;
      padding: 25px;
      background: #f8f9fa;
      border-radius: 15px;
      border-left: 5px solid #667eea;
    }

    .demo-section h2 {
      color: #333;
      margin-bottom: 20px;
      font-size: 1.5em;
    }

    .controls {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    button {
      padding: 12px 24px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    input[type="file"] {
      padding: 10px;
      border: 2px solid #ddd;
      border-radius: 8px;
      background: white;
    }

    .canvas-container {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .canvas-wrapper {
      text-align: center;
    }

    canvas {
      border: 2px solid #ddd;
      border-radius: 10px;
      background: white;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      max-width: 100%;
      height: auto;
    }

    .canvas-label {
      margin-top: 10px;
      font-weight: 600;
      color: #555;
    }

    .status {
      padding: 15px;
      margin: 20px 0;
      border-radius: 8px;
      font-weight: 500;
    }

    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status.warning {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    .metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .metric-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .metric-value {
      font-size: 2em;
      font-weight: bold;
      color: #667eea;
    }

    .metric-label {
      color: #666;
      margin-top: 5px;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      margin: 10px 0;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(45deg, #667eea, #764ba2);
      transition: width 0.3s ease;
      width: 0%;
    }

    @media (max-width: 768px) {
      .container {
        padding: 20px;
      }

      h1 {
        font-size: 2em;
      }

      .controls {
        flex-direction: column;
      }

      .canvas-container {
        flex-direction: column;
        align-items: center;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>🚀 WebGL ONNX 推理库演示</h1>

    <!-- 系统检查 -->
    <div class="demo-section">
      <h2>🔧 系统兼容性检查</h2>
      <div id="webgl-status" class="status info">正在检查WebGL支持...</div>
      <div class="controls">
        <button onclick="checkSystemCapabilities()">重新检查</button>
        <label
          style="display: flex; align-items: center; gap: 8px; padding: 8px 16px; background: #f8f9fa; border-radius: 8px; cursor: pointer;">
          <input type="checkbox" id="debug-mode-toggle" onchange="toggleDebugMode(this.checked)">
          <span>Debug 模式 (可能影响性能)</span>
        </label>
      </div>
    </div>

    <!-- 模型加载 -->
    <div class="demo-section">
      <h2>📦 模型加载</h2>
      <div class="controls">
        <input type="file" id="model-file" accept=".onnx" onchange="handleModelUpload(event)">
        <button onclick="loadDefaultModel()">加载默认模型</button>
        <button onclick="loadSimpleModel()">加载简单测试模型</button>
        <button onclick="loadSubTestModel()">加载Sub测试模型</button>
      </div>
      <div id="model-status" class="status info">请选择或加载ONNX模型文件</div>
      <div id="model-info" style="display: none;">
        <h3>模型信息：</h3>
        <div id="model-details"></div>
      </div>
    </div>

    <!-- 图片输入 -->
    <div class="demo-section">
      <h2>🖼️ 图片输入</h2>
      <div class="controls">
        <input type="file" id="image-file" accept="image/*" onchange="handleImageUpload(event)">
        <button onclick="loadDefaultImage()">加载默认图片</button>
        <button onclick="captureFromCamera()" id="camera-btn">摄像头捕获</button>
      </div>
      <div class="canvas-container">
        <div class="canvas-wrapper">
          <canvas id="input-canvas" width="512" height="512"></canvas>
          <div class="canvas-label">输入图片</div>
        </div>
      </div>
    </div>

    <!-- 推理执行 -->
    <div class="demo-section">
      <h2>⚡ 推理执行</h2>
      <div class="controls">
        <button onclick="runInference()" id="inference-btn" disabled>开始推理</button>
        <button onclick="runBenchmark()" id="benchmark-btn" disabled>性能测试</button>
        <button onclick="clearResults()">清除结果</button>
      </div>
      <div id="inference-status" class="status info">请先加载模型和图片</div>
      <div class="progress-bar" id="progress-container" style="display: none;">
        <div class="progress-fill" id="progress-fill"></div>
      </div>
    </div>

    <!-- 结果展示 -->
    <div class="demo-section">
      <h2>📊 推理结果</h2>
      <div class="canvas-container">
        <div class="canvas-wrapper">
          <canvas id="output-canvas" width="512" height="512"></canvas>
          <div class="canvas-label">灰度输出</div>
        </div>
      </div>
    </div>

    <!-- 中间结果展示 -->
    <div class="demo-section" id="intermediate-results-section">
      <h2>🔄 中间推理步骤</h2>
      <div class="controls">
        <button onclick="toggleIntermediateView()">切换视图</button>
        <button onclick="clearIntermediateResults()">清除中间结果</button>
      </div>
      <div id="intermediate-container" style="overflow-x: auto; padding: 10px; display: none;">
        <canvas id="intermediate-canvas" style="border: 1px solid #ddd; background: #f0f0f0;"></canvas>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="demo-section">
      <h2>📈 性能指标</h2>
      <div class="metrics">
        <div class="metric-card">
          <div class="metric-value" id="inference-time">--</div>
          <div class="metric-label">推理时间 (ms)</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="memory-usage">--</div>
          <div class="metric-label">内存使用 (MB)</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="fps">--</div>
          <div class="metric-label">FPS</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="total-inferences">0</div>
          <div class="metric-label">总推理次数</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 摄像头模态框 -->
  <div id="camera-modal"
    style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
    <div
      style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 10px;">
      <h3>摄像头捕获</h3>
      <video id="camera-video" width="400" height="300" autoplay></video>
      <br><br>
      <button onclick="capturePhoto()">拍照</button>
      <button onclick="closeCameraModal()">关闭</button>
    </div>
  </div>

  <script type="module" src="./demo.js"></script>
</body>

</html>