<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>模型加载调试</title>
</head>
<body>
    <h1>模型加载调试</h1>
    <div id="log"></div>
    
    <script type="module">
        function log(message) {
            const logElement = document.getElementById('log');
            logElement.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        async function testModelLoading() {
            try {
                log('开始测试模型加载...');
                
                // 1. 导入推理库
                const module = await import('./dist/webgl-onnx-inference.es.js?v=' + Date.now());
                const { WebGLONNXInference } = module;
                log('✅ 推理库导入成功');
                
                // 2. 创建推理实例
                const inference = new WebGLONNXInference();
                log('✅ 推理实例创建成功');
                
                // 3. 初始化
                const canvas = document.createElement('canvas');
                const initSuccess = await inference.initialize({ canvas });
                log('推理库初始化: ' + initSuccess);
                
                if (!initSuccess) {
                    throw new Error('推理库初始化失败');
                }
                
                // 4. 加载模型
                log('开始加载模型...');
                const response = await fetch('/model.onnx');
                const arrayBuffer = await response.arrayBuffer();
                log('模型文件下载完成: ' + arrayBuffer.byteLength + ' bytes');
                
                // 直接调用模型解析器进行调试
                const { ONNXModelParser } = module;
                const parser = new ONNXModelParser();
                
                log('开始解析模型...');
                await parser.loadModel(arrayBuffer);
                log('✅ 模型解析成功!');
                
                const modelInfo = parser.getModelInfo();
                log('模型信息: ' + JSON.stringify(modelInfo, null, 2));
                
            } catch (error) {
                log('❌ 错误: ' + error.message);
                log('错误堆栈: ' + error.stack);
                console.error('详细错误:', error);
            }
        }
        
        // 页面加载后开始测试
        document.addEventListener('DOMContentLoaded', testModelLoading);
    </script>
</body>
</html>