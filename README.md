# WebGL ONNX 推理库

一个纯WebGL实现的ONNX模型推理库，通过GPU纹理计算实现高性能的神经网络推理，完全避免CPU操作。

## ✨ 特性

- 🚀 **纯WebGL实现** - 完全基于WebGL 2.0，无CPU运算瓶颈
- 🎨 **基于纹理的输入输出** - 高效的GPU内存管理
- 📦 **支持ONNX模型格式** - 兼容主流深度学习框架导出的模型
- ⚡ **实时推理能力** - 优化的GPU计算流水线
- 🔧 **TypeScript实现** - 完整的类型安全和开发体验
- 📊 **性能监控** - 实时推理时间和内存使用统计
- 🛡️ **渲染结果检查** - 自动检测全黑或全白的异常推理结果
- 🔍 **中间结果监控** - 在推理过程的每一步检查中间结果（Debug模式）
- 🖼️ **中间结果可视化** - 可视化显示推理过程中每一步的输出

## 🛠️ 技术栈

- **WebGL 2.0** - GPU计算核心
- **TypeScript** - 类型安全的开发语言
- **Protocol Buffers** - ONNX模型解析
- **Vite** - 现代化构建工具

## 📋 系统要求

- 现代浏览器支持WebGL 2.0
- 必需的WebGL扩展：
  - `EXT_color_buffer_float`
  - `OES_texture_float_linear` 
  - `EXT_float_blend`

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 构建项目

```bash
npm run build
```

### 启动演示

```bash
npm run dev
```

访问 http://localhost:3000 查看演示页面。

## 💻 使用示例

```javascript
import { WebGLONNXInference } from './dist/webgl-onnx-inference.es.js';

// 初始化推理引擎（启用Debug模式以进行中间结果检查和可视化）
const canvas = document.getElementById('canvas');
const inference = new WebGLONNXInference();

const config = {
  canvas: canvas,
  precision: 'highp',
  enableOptimizations: true,
  maxTextureSize: 4096,
  batchSize: 1,
  debugMode: true  // 启用Debug模式以进行中间结果检查和可视化
};

await inference.initialize(config);

// 加载ONNX模型
const response = await fetch('/path/to/model.onnx');
const modelBuffer = await response.arrayBuffer();
const modelInfo = await inference.loadModel(modelBuffer);

// 设置输入图片
const img = new Image();
img.src = '/path/to/image.jpg';
await new Promise(resolve => img.onload = resolve);
inference.setInputFromImage(img);

// 执行推理（在Debug模式下会检查每一步的中间结果）
const result = await inference.inference();
console.log(`推理时间: ${result.inferenceTime}ms`);

// 获取中间结果用于可视化
if (config.debugMode) {
  const intermediateResults = await inference.getIntermediateResults();
  // 在UI中显示中间结果
  displayIntermediateResults(intermediateResults);
}

// 渲染灰度图结果到Canvas并检查异常
const outputCanvas = document.getElementById('output');
try {
  inference.renderToCanvasWithCheck(outputCanvas);
  console.log('推理结果渲染成功');
} catch (error) {
  console.error('推理结果异常:', error.message);
}
```

## 🏗️ 架构设计

### 核心模块

- **WebGLContextManager** - WebGL上下文管理和资源控制
- **TextureManager** - 纹理池化和内存管理
- **ONNXModelParser** - ONNX模型解析和图构建
- **ShaderGenerator** - 动态着色器生成和编译
- **OperatorMapper** - 操作符到WebGL实现的映射
- **GPUInferenceEngine** - GPU推理执行引擎

### 数据流

```
输入图片 → 纹理上传 → GPU计算图执行 → 输出纹理 → Canvas渲染 → 异常检测
               ↑                              ↓
         中间结果检查（Debug模式）      中间结果可视化（Debug模式）
```

## 🧪 支持的操作符

当前版本支持以下ONNX操作符：

- **Conv2D** - 二维卷积
- **Relu** - ReLU激活函数  
- **MaxPool2D** - 最大池化
- **BatchNormalization** - 批归一化
- **Add** - 逐元素加法
- **Mul** - 逐元素乘法
- **Softmax** - Softmax归一化
- **Reshape** - 张量形状变换

## 📊 性能指标

演示页面包含实时性能监控：

- **推理时间** - 单次推理耗时（毫秒）
- **内存使用** - GPU纹理内存占用（MB）
- **FPS** - 基于平均推理时间计算的帧率
- **总推理次数** - 累计执行次数

## 🛡️ 渲染结果检查

新版本增加了自动检测异常推理结果的功能：

### 检测机制
- 自动检测全黑图像（所有像素值接近0）
- 自动检测全白图像（所有像素值接近255）
- 忽略透明像素区域
- 提供详细的检测结果信息

### 使用方法
```javascript
// 检查渲染结果
const checkResult = inference.checkRenderResult();
if (checkResult.isAllBlack) {
  console.warn('推理结果全黑，可能存在模型问题');
} else if (checkResult.isAllWhite) {
  console.warn('推理结果全白，可能存在模型问题');
}

// 渲染并自动检查（推荐）
try {
  inference.renderToCanvasWithCheck(outputCanvas);
  console.log('渲染成功且结果正常');
} catch (error) {
  console.error('渲染失败或结果异常:', error.message);
}
```

## 🔍 中间结果监控

在Debug模式下，推理引擎会在每一步计算后检查中间结果，及时发现异常：

### 功能特点
- **逐节点检查** - 在每个计算节点执行后检查输出纹理
- **浮点精度处理** - 针对浮点纹理数据进行精确比较
- **性能优化** - 仅在Debug模式下启用，不影响生产环境性能
- **详细日志** - 提供具体的节点信息和异常警告

### 启用方式
```javascript
// 在初始化时启用Debug模式
const config = {
  // ... 其他配置
  debugMode: true  // 启用Debug模式
};

await inference.initialize(config);
```

当检测到异常中间结果时，控制台会输出类似以下的警告信息：
```
执行节点: conv1 (类型: Conv)
使用节点输入: input
警告: 节点 conv1 输出全黑
节点 conv1 执行完成
```

## 🖼️ 中间结果可视化

在Debug模式下，可以可视化显示推理过程中每一步的输出：

### 功能特点
- **实时显示** - 在推理执行过程中实时捕获并显示中间结果
- **网格布局** - 将多个中间结果以网格形式排列显示
- **标签标识** - 每个中间结果都带有节点类型和ID标签
- **交互控制** - 可以切换显示/隐藏中间结果视图

### 使用方法
```javascript
// 启用Debug模式后执行推理
const result = await inference.inference();

// 获取中间结果
const intermediateResults = await inference.getIntermediateResults();

// 在UI中显示（演示页面自动处理）
displayIntermediateResults(intermediateResults);
```

在演示页面中：
1. 启用Debug模式
2. 运行推理
3. 点击"切换视图"按钮显示中间结果
4. 可以看到每个计算步骤的输出图像

## 🔧 开发命令

```bash
# 类型检查
npm run type-check

# 代码格式化
npm run lint:fix

# 清理构建
npm run clean

# 开发服务器
npm run dev

# 生产构建
npm run build

# 运行测试
npm run test
```

## 📁 项目结构

```
src/
├── core/           # 核心引擎
├── webgl/          # WebGL管理
├── onnx/           # ONNX解析
├── operators/      # 操作符实现
├── shaders/        # 着色器模板
├── types/          # 类型定义
├── errors/         # 错误处理
└── utils/          # 工具函数
```

## 🌟 特色功能

### 纹理池化管理
- 自动纹理复用减少GPU内存分配开销
- 智能内存回收机制
- 支持不同精度格式的纹理管理

### 动态着色器生成
- 根据操作符类型动态生成优化的着色器代码
- 支持循环展开等性能优化
- 着色器编译缓存机制

### 错误处理和诊断
- 详细的错误类型分类
- WebGL状态检查和错误报告
- 模型兼容性验证
- 渲染结果异常检测
- 中间结果监控（Debug模式）
- 中间结果可视化（Debug模式）

## 🚧 已知限制

- 目前模型文件为空，需要提供有效的ONNX模型进行测试
- 部分高级操作符尚未实现
- 动态形状支持有限
- 主要针对图像处理任务优化

## 🔮 未来计划

- [ ] 支持更多ONNX操作符
- [ ] 动态批处理支持  
- [ ] WebGPU后端实现
- [ ] 模型量化支持
- [ ] 更多示例和教程

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**注意**: 这是一个演示项目，展示了WebGL ONNX推理的技术可行性。在生产环境使用前，建议进行充分的测试和优化。