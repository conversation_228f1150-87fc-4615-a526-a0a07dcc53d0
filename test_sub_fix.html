<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub操作符修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #007bff; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-failure {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Sub操作符修复测试</h1>
        
        <p>这个测试验证Sub操作符是否已正确修复，能够成功编译着色器程序。</p>
        
        <button onclick="runSubTest()">测试Sub操作符</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="test-results"></div>
        <div id="log" class="log">点击"测试Sub操作符"开始测试...</div>
    </div>

    <script type="module">
        let logElement = document.getElementById('log');
        let resultsElement = document.getElementById('test-results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function addTestResult(title, success, message) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'test-success' : 'test-failure'}`;
            resultDiv.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${title}</strong><br>
                ${message}
            `;
            resultsElement.appendChild(resultDiv);
        }
        
        window.clearLog = function() {
            logElement.innerHTML = '';
            resultsElement.innerHTML = '';
        };
        
        window.runSubTest = async function() {
            clearLog();
            log('🧪 开始Sub操作符修复测试...', 'info');
            
            let testsRun = 0;
            let testsPassed = 0;
            
            try {
                // 测试1: 检查推理库导入
                log('测试1: 检查推理库导入...', 'info');
                testsRun++;
                
                try {
                    const module = await import('./dist/webgl-onnx-inference.es.js');
                    const { WebGLONNXInference, checkWebGLSupport } = module;
                    log('✅ 推理库导入成功', 'success');
                    addTestResult('推理库导入', true, '成功导入WebGLONNXInference模块');
                    testsPassed++;
                } catch (error) {
                    log(`❌ 推理库导入失败: ${error.message}`, 'error');
                    addTestResult('推理库导入', false, error.message);
                    return;
                }
                
                // 测试2: WebGL支持检查
                log('测试2: 检查WebGL支持...', 'info');
                testsRun++;
                
                const { checkWebGLSupport } = await import('./dist/webgl-onnx-inference.es.js');
                const webglSupported = checkWebGLSupport();
                
                if (webglSupported) {
                    log('✅ WebGL 2.0 支持正常', 'success');
                    addTestResult('WebGL支持', true, 'WebGL 2.0已支持');
                    testsPassed++;
                } else {
                    log('❌ WebGL 2.0 不支持', 'error');
                    addTestResult('WebGL支持', false, 'WebGL 2.0不支持，无法继续测试');
                    return;
                }
                
                // 测试3: 推理库初始化
                log('测试3: 初始化推理库...', 'info');
                testsRun++;
                
                const { WebGLONNXInference } = await import('./dist/webgl-onnx-inference.es.js');
                const inference = new WebGLONNXInference();
                
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 512;
                
                const config = {
                    canvas: canvas,
                    precision: 'highp',
                    enableOptimizations: true,
                    maxTextureSize: 2048,
                    batchSize: 1
                };
                
                const initSuccess = await inference.initialize(config);
                
                if (initSuccess) {
                    log('✅ 推理库初始化成功', 'success');
                    addTestResult('推理库初始化', true, '推理库成功初始化');
                    testsPassed++;
                } else {
                    log('❌ 推理库初始化失败', 'error');
                    addTestResult('推理库初始化', false, '推理库初始化失败');
                    return;
                }
                
                // 测试4: 加载Sub测试模型
                log('测试4: 加载Sub测试模型...', 'info');
                testsRun++;
                
                try {
                    const response = await fetch('/simple_sub.onnx');
                    if (!response.ok) {
                        throw new Error('Sub测试模型文件不存在');
                    }
                    
                    const arrayBuffer = await response.arrayBuffer();
                    log(`📦 Sub模型文件大小: ${arrayBuffer.byteLength} 字节`, 'info');
                    
                    // 这里是关键测试 - 如果Sub操作符修复了，模型加载应该成功
                    const modelInfo = await inference.loadModel(arrayBuffer);
                    
                    log('✅ Sub测试模型加载成功!', 'success');
                    log(`📊 模型信息: 输入${modelInfo.inputShape}, 输出${modelInfo.outputShape}, 操作符${modelInfo.operatorCount}个`, 'info');
                    
                    addTestResult('Sub模型加载', true, 
                        `Sub操作符修复成功！模型包含${modelInfo.operatorCount}个操作符，成功编译所有着色器程序。`);
                    testsPassed++;
                    
                } catch (modelError) {
                    log(`❌ Sub模型加载失败: ${modelError.message}`, 'error');
                    addTestResult('Sub模型加载', false, 
                        `Sub操作符仍有问题: ${modelError.message}`);
                }
                
                // 清理资源
                inference.dispose();
                log('🗑️ 资源清理完成', 'info');
                
                // 测试总结
                log('', 'info');
                log('=== 测试总结 ===', 'info');
                log(`总测试数: ${testsRun}`, 'info');
                log(`通过测试: ${testsPassed}`, 'success');
                log(`失败测试: ${testsRun - testsPassed}`, testsPassed === testsRun ? 'info' : 'error');
                log(`成功率: ${((testsPassed / testsRun) * 100).toFixed(1)}%`, 
                    testsPassed === testsRun ? 'success' : 'warning');
                
                if (testsPassed === testsRun) {
                    log('🎉 所有测试通过！Sub操作符修复成功！', 'success');
                } else {
                    log('⚠️ 部分测试失败，请检查问题', 'warning');
                }
                
            } catch (error) {
                log(`❌ 测试执行失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
                addTestResult('测试执行', false, `测试执行异常: ${error.message}`);
            }
        };
    </script>
</body>
</html>