import {
  WebGLONNXInference,
  InferenceError,
  InferenceErrorType,
  checkWebGLSupport,
  getWebGLCapabilities
} from './src/index.ts';

// 全局状态
let inference = null;
let isModelLoaded = false;
let isImageLoaded = false;
let currentStream = null;
let debugMode = false; // debug 模式状态
let intermediateResults = []; // 存储中间结果
let showIntermediateResults = false; // 是否显示中间结果

// DOM元素
const elements = {
  webglStatus: document.getElementById('webgl-status'),
  modelStatus: document.getElementById('model-status'),
  modelInfo: document.getElementById('model-info'),
  modelDetails: document.getElementById('model-details'),
  inferenceStatus: document.getElementById('inference-status'),
  inferenceBtn: document.getElementById('inference-btn'),
  benchmarkBtn: document.getElementById('benchmark-btn'),
  inputCanvas: document.getElementById('input-canvas'),
  outputCanvas: document.getElementById('output-canvas'),
  progressContainer: document.getElementById('progress-container'),
  progressFill: document.getElementById('progress-fill'),
  cameraModal: document.getElementById('camera-modal'),
  cameraVideo: document.getElementById('camera-video'),

  // 中间结果元素
  intermediateSection: document.getElementById('intermediate-results-section'),
  intermediateCanvas: document.getElementById('intermediate-canvas'),
  intermediateContainer: document.getElementById('intermediate-container'),

  // 性能指标
  inferenceTime: document.getElementById('inference-time'),
  memoryUsage: document.getElementById('memory-usage'),
  fps: document.getElementById('fps'),
  totalInferences: document.getElementById('total-inferences')
};

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 WebGL ONNX 推理库演示启动');

  // 检查系统兼容性
  await checkSystemCapabilities();

  // 初始化推理库
  await initializeInference();
});

/**
 * 检查系统兼容性
 */
window.checkSystemCapabilities = async function () {
  try {
    elements.webglStatus.textContent = '正在检查WebGL支持...';
    elements.webglStatus.className = 'status info';

    // 检查WebGL支持
    const webglSupported = checkWebGLSupport();
    if (!webglSupported) {
      throw new Error('WebGL 2.0 不支持');
    }

    // 获取WebGL能力
    const capabilities = getWebGLCapabilities();
    if (!capabilities) {
      throw new Error('无法获取WebGL能力信息');
    }

    // 显示成功信息
    elements.webglStatus.innerHTML = `
            ✅ WebGL 2.0 支持正常<br>
            📐 最大纹理尺寸: ${capabilities.maxTextureSize}x${capabilities.maxTextureSize}<br>
            🔧 支持扩展: ${capabilities.extensions.length} 个
        `;
    elements.webglStatus.className = 'status success';

    console.log('WebGL 能力:', capabilities);

  } catch (error) {
    elements.webglStatus.innerHTML = `❌ 系统不兼容: ${error.message}`;
    elements.webglStatus.className = 'status error';
    console.error('系统兼容性检查失败:', error);
  }
};

/**
 * 初始化推理库
 */
async function initializeInference() {
  try {
    inference = new WebGLONNXInference();

    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;

    const config = {
      canvas: canvas,
      precision: 'highp',
      enableOptimizations: true,
      maxTextureSize: 4096,
      batchSize: 1,
      debugMode: debugMode // 使用全局 debug 模式设置
    };

    const success = await inference.initialize(config);
    if (!success) {
      inference = null;
      throw new Error('推理库初始化失败');
    }

    console.log('✅ 推理库初始化成功');

  } catch (error) {
    console.error('推理库初始化失败:', error);
    inference = null;
    showStatus('error', `推理库初始化失败: ${error.message}`);
  }
}

/**
 * 处理模型上传
 */
window.handleModelUpload = async function (event) {
  const file = event.target.files[0];
  if (!file) return;

  try {
    showStatus('info', '正在加载模型...', elements.modelStatus);

    const arrayBuffer = await file.arrayBuffer();
    const modelInfo = await inference.loadModel(arrayBuffer);

    // 显示模型信息
    elements.modelDetails.innerHTML = `
            <p><strong>输入形状:</strong> [${modelInfo.inputShape.join(', ')}]</p>
            <p><strong>输出形状:</strong> [${modelInfo.outputShape.join(', ')}]</p>
            <p><strong>操作符数量:</strong> ${modelInfo.operatorCount}</p>
            <p><strong>模型大小:</strong> ${(modelInfo.modelSize / 1024 / 1024).toFixed(2)} MB</p>
        `;
    elements.modelInfo.style.display = 'block';

    isModelLoaded = true;
    showStatus('success', '✅ 模型加载成功', elements.modelStatus);
    updateButtons();

  } catch (error) {
    console.error('模型加载失败:', error);
    showStatus('error', `模型加载失败: ${error.message}`, elements.modelStatus);
  }
};

/**
 * 加载默认模型
 */
window.loadDefaultModel = async function () {
  try {
    if (!inference) {
      throw new Error('推理库未初始化，请刷新页面重试');
    }

    showStatus('info', '正在加载默认模型...', elements.modelStatus);

    const response = await fetch('/model.onnx');
    if (!response.ok) {
      throw new Error('默认模型文件不存在或无法访问');
    }

    const arrayBuffer = await response.arrayBuffer();

    // 检查文件是否为空
    if (arrayBuffer.byteLength === 0) {
      throw new Error('默认模型文件为空，请上传有效的ONNX模型');
    }

    const modelInfo = await inference.loadModel(arrayBuffer);

    // 显示模型信息
    elements.modelDetails.innerHTML = `
            <p><strong>输入形状:</strong> [${modelInfo.inputShape.join(', ')}]</p>
            <p><strong>输出形状:</strong> [${modelInfo.outputShape.join(', ')}]</p>
            <p><strong>操作符数量:</strong> ${modelInfo.operatorCount}</p>
            <p><strong>模型大小:</strong> ${(modelInfo.modelSize / 1024 / 1024).toFixed(2)} MB</p>
        `;
    elements.modelInfo.style.display = 'block';

    isModelLoaded = true;
    showStatus('success', '✅ 默认模型加载成功', elements.modelStatus);
    updateButtons();

  } catch (error) {
    console.error('默认模型加载失败:', error);
    if (error.message.includes('默认模型文件为空')) {
      showStatus('warning', '⚠️ 默认模型文件为空，请上传有效的ONNX模型文件', elements.modelStatus);
    } else {
      showStatus('error', `默认模型加载失败: ${error.message}`, elements.modelStatus);
    }
  }
};

/**
 * 加载简单测试模型
 */
window.loadSimpleModel = async function () {
  try {
    showStatus('info', '正在加载简单测试模型...', elements.modelStatus);

    const response = await fetch('/simple_add.onnx');
    if (!response.ok) {
      throw new Error('简单测试模型文件不存在');
    }

    const arrayBuffer = await response.arrayBuffer();
    const modelInfo = await inference.loadModel(arrayBuffer);

    // 显示模型信息
    elements.modelDetails.innerHTML = `
            <p><strong>输入形状:</strong> [${modelInfo.inputShape.join(', ')}]</p>
            <p><strong>输出形状:</strong> [${modelInfo.outputShape.join(', ')}]</p>
            <p><strong>操作符数量:</strong> ${modelInfo.operatorCount}</p>
            <p><strong>模型大小:</strong> ${(arrayBuffer.byteLength / 1024).toFixed(2)} KB</p>
            <p><strong>模型类型:</strong> 简单加法测试模型</p>
        `;
    elements.modelInfo.style.display = 'block';

    isModelLoaded = true;
    showStatus('success', '✅ 简单测试模型加载成功', elements.modelStatus);
    updateButtons();

  } catch (error) {
    console.error('简单测试模型加载失败:', error);
    showStatus('error', `简单测试模型加载失败: ${error.message}`, elements.modelStatus);
  }
};

/**
 * 加载Sub测试模型
 */
window.loadSubTestModel = async function () {
  try {
    showStatus('info', '正在加载Sub测试模型...', elements.modelStatus);

    const response = await fetch('/simple_sub.onnx');
    if (!response.ok) {
      throw new Error('Sub测试模型文件不存在');
    }

    const arrayBuffer = await response.arrayBuffer();
    const modelInfo = await inference.loadModel(arrayBuffer);

    // 显示模型信息
    elements.modelDetails.innerHTML = `
            <p><strong>输入形状:</strong> [${modelInfo.inputShape.join(', ')}]</p>
            <p><strong>输出形状:</strong> [${modelInfo.outputShape.join(', ')}]</p>
            <p><strong>操作符数量:</strong> ${modelInfo.operatorCount}</p>
            <p><strong>模型大小:</strong> ${(arrayBuffer.byteLength / 1024).toFixed(2)} KB</p>
            <p><strong>模型类型:</strong> Sub减法测试模型</p>
        `;
    elements.modelInfo.style.display = 'block';

    isModelLoaded = true;
    showStatus('success', '✅ Sub测试模型加载成功', elements.modelStatus);
    updateButtons();

  } catch (error) {
    console.error('Sub测试模型加载失败:', error);
    showStatus('error', `Sub测试模型加载失败: ${error.message}`, elements.modelStatus);
  }
};

/**
 * 处理图片上传
 */
window.handleImageUpload = function (event) {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function (e) {
    const img = new Image();
    img.onload = function () {
      drawImageToCanvas(img, elements.inputCanvas);
      isImageLoaded = true;
      updateButtons();
    };
    img.src = e.target.result;
  };
  reader.readAsDataURL(file);
};

/**
 * 加载默认图片
 */
window.loadDefaultImage = function () {
  const img = new Image();
  img.onload = function () {
    drawImageToCanvas(img, elements.inputCanvas);
    isImageLoaded = true;
    updateButtons();
    console.log('✅ 默认图片加载成功');
  };

  img.onerror = function () {
    console.warn('默认图片加载失败，创建测试图案');
    createTestPattern(elements.inputCanvas);
    isImageLoaded = true;
    updateButtons();
  };

  img.crossOrigin = 'anonymous';
  img.src = '/public/man.png';
};

/**
 * 绘制图片到Canvas
 */
function drawImageToCanvas(img, canvas) {
  const ctx = canvas.getContext('2d');

  // 计算缩放比例以保持宽高比
  const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
  const width = img.width * scale;
  const height = img.height * scale;
  const x = (canvas.width - width) / 2;
  const y = (canvas.height - height) / 2;

  // 清空画布并绘制图片
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(img, x, y, width, height);
}

/**
 * 创建测试图案
 */
function createTestPattern(canvas) {
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;

  // 清空背景
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, width, height);

  // 绘制彩色方格
  const gridSize = 64;
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];

  for (let x = 0; x < width; x += gridSize) {
    for (let y = 0; y < height; y += gridSize) {
      const colorIndex = Math.floor((x + y) / gridSize) % colors.length;
      ctx.fillStyle = colors[colorIndex];
      ctx.fillRect(x, y, gridSize, gridSize);
    }
  }

  // 添加文本
  ctx.fillStyle = '#333';
  ctx.font = 'bold 48px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('测试图案', width / 2, height / 2);

  console.log('✅ 创建测试图案成功');
}

/**
 * 摄像头捕获
 */
window.captureFromCamera = async function () {
  try {
    currentStream = await navigator.mediaDevices.getUserMedia({
      video: { width: 640, height: 480 }
    });
    elements.cameraVideo.srcObject = currentStream;
    elements.cameraModal.style.display = 'block';
  } catch (error) {
    console.error('摄像头访问失败:', error);
    alert('无法访问摄像头: ' + error.message);
  }
};

/**
 * 拍照
 */
window.capturePhoto = function () {
  const video = elements.cameraVideo;
  const canvas = elements.inputCanvas;
  const ctx = canvas.getContext('2d');

  ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  isImageLoaded = true;
  updateButtons();
  closeCameraModal();
};

/**
 * 关闭摄像头模态框
 */
window.closeCameraModal = function () {
  if (currentStream) {
    currentStream.getTracks().forEach(track => track.stop());
    currentStream = null;
  }
  elements.cameraModal.style.display = 'none';
};

/**
 * 执行推理
 */
window.runInference = async function () {
  if (!isModelLoaded || !isImageLoaded) {
    showStatus('error', '请先加载模型和图片', elements.inferenceStatus);
    return;
  }

  try {
    showStatus('info', '正在执行推理...', elements.inferenceStatus);
    showProgress(0);

    // 清空之前的中间结果
    intermediateResults = [];

    // 设置输入
    inference.setInputFromCanvas(elements.inputCanvas);
    showProgress(30);

    // 执行推理
    const result = await inference.inference();
    showProgress(80);

    // 如果启用了Debug模式，收集中间结果用于后续显示
    if (debugMode) {
      // 获取中间结果（这需要修改推理库以支持返回中间结果）
      const intermediateData = await inference.getIntermediateResults();
      if (intermediateData && intermediateData.length > 0) {
        intermediateResults = intermediateData;
        // 如果需要显示中间结果，则立即显示
        if (showIntermediateResults) {
          displayIntermediateResults();
        }
      }
    }

    // 渲染结果并检查
    try {
      const checkResult = inference.renderToCanvasWithCheck(elements.outputCanvas);
      showProgress(100);

      // 更新性能指标
      updateMetrics(result);

      // 添加渲染检查结果到状态消息
      let statusMessage = `✅ 推理完成！耗时 ${result.inferenceTime.toFixed(2)}ms`;
      if (checkResult.isAllBlack) {
        statusMessage += '\n⚠️ 警告：推理结果全黑';
      } else if (checkResult.isAllWhite) {
        statusMessage += '\n⚠️ 警告：推理结果全白';
      }

      showStatus('success', statusMessage, elements.inferenceStatus);
    } catch (renderError) {
      showProgress(100);
      updateMetrics(result);
      showStatus('warning',
        `✅ 推理完成但渲染检查失败: ${renderError.message}\n耗时 ${result.inferenceTime.toFixed(2)}ms`,
        elements.inferenceStatus);
    }

    setTimeout(() => hideProgress(), 1000);

  } catch (error) {
    console.error('推理执行失败:', error);
    showStatus('error', `推理执行失败: ${error.message}`, elements.inferenceStatus);
    hideProgress();
  }
};

/**
 * 显示中间结果
 */
function displayIntermediateResults() {
  if (intermediateResults.length === 0) return;

  // 显示中间结果容器
  elements.intermediateContainer.style.display = 'block';

  // 计算Canvas尺寸
  const canvas = elements.intermediateCanvas;
  const ctx = canvas.getContext('2d');

  // 每个小图像的尺寸
  const thumbSize = 128;
  const spacing = 20; // 增加间距以避免标签被遮挡
  const labelHeight = 30; // 为标签预留更多空间

  // 计算总尺寸
  const totalWidth = Math.min(intermediateResults.length * (thumbSize + spacing), window.innerWidth - 100);
  const cols = Math.floor(totalWidth / (thumbSize + spacing));
  const rows = Math.ceil(intermediateResults.length / cols);
  const totalHeight = rows * (thumbSize + spacing + labelHeight) + spacing;

  canvas.width = totalWidth;
  canvas.height = totalHeight;

  // 清空Canvas
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 绘制每个中间结果
  intermediateResults.forEach((result, index) => {
    const col = index % cols;
    const row = Math.floor(index / cols);

    const x = col * (thumbSize + spacing) + spacing;
    const y = row * (thumbSize + spacing + labelHeight) + spacing;

    // 绘制缩略图
    if (result.imageData) {
      // 创建临时Canvas来缩放图像
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = thumbSize;
      tempCanvas.height = thumbSize;
      const tempCtx = tempCanvas.getContext('2d');
      tempCtx.putImageData(result.imageData, 0, 0);

      ctx.drawImage(tempCanvas, x, y, thumbSize, thumbSize);
    }

    // 绘制标签
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(result.label || `Step ${index + 1}`, x + thumbSize / 2, y + thumbSize + 20);
  });
}

/**
 * 切换中间结果视图
 */
window.toggleIntermediateView = function () {
  showIntermediateResults = !showIntermediateResults;

  if (showIntermediateResults) {
    // 如果之前已经运行过推理，直接显示结果
    if (intermediateResults.length > 0) {
      elements.intermediateContainer.style.display = 'block';
      displayIntermediateResults();
    } else {
      showStatus('info', '请重新运行推理以收集中间结果', elements.inferenceStatus);
    }
  } else {
    elements.intermediateContainer.style.display = 'none';
  }
};

/**
 * 清除中间结果
 */
window.clearIntermediateResults = function () {
  intermediateResults = [];
  elements.intermediateSection.style.display = 'none';
  const ctx = elements.intermediateCanvas.getContext('2d');
  ctx.clearRect(0, 0, elements.intermediateCanvas.width, elements.intermediateCanvas.height);
};

/**
 * 性能测试
 */
window.runBenchmark = async function () {
  if (!isModelLoaded || !isImageLoaded) {
    showStatus('error', '请先加载模型和图片', elements.inferenceStatus);
    return;
  }

  const iterations = 10;
  const times = [];

  try {
    showStatus('info', `正在进行性能测试 (${iterations} 次推理)...`, elements.inferenceStatus);

    inference.setInputFromCanvas(elements.inputCanvas);

    for (let i = 0; i < iterations; i++) {
      showProgress((i / iterations) * 100);

      const result = await inference.inference();
      times.push(result.inferenceTime);

      // 更新界面
      elements.inferenceTime.textContent = result.inferenceTime.toFixed(2);
      elements.totalInferences.textContent = inference.getPerformanceMetrics().totalInferences;
    }

    // 计算统计数据
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    // 渲染最后一次结果并检查
    try {
      const checkResult = inference.renderToCanvasWithCheck(elements.outputCanvas);
      let statusMessage = `✅ 性能测试完成！\n` +
        `平均时间: ${avgTime.toFixed(2)}ms\n` +
        `最短时间: ${minTime.toFixed(2)}ms\n` +
        `最长时间: ${maxTime.toFixed(2)}ms\n` +
        `FPS: ${(1000 / avgTime).toFixed(1)}`;

      if (checkResult.isAllBlack) {
        statusMessage += '\n⚠️ 警告：推理结果全黑';
      } else if (checkResult.isAllWhite) {
        statusMessage += '\n⚠️ 警告：推理结果全白';
      }

      showStatus('success', statusMessage, elements.inferenceStatus);
    } catch (renderError) {
      showStatus('warning',
        `✅ 性能测试完成但渲染检查失败: ${renderError.message}\n` +
        `平均时间: ${avgTime.toFixed(2)}ms\n` +
        `FPS: ${(1000 / avgTime).toFixed(1)}`,
        elements.inferenceStatus
      );
    }

    setTimeout(() => hideProgress(), 1000);

  } catch (error) {
    console.error('性能测试失败:', error);
    showStatus('error', `性能测试失败: ${error.message}`, elements.inferenceStatus);
    hideProgress();
  }
};

/**
 * 清除结果
 */
window.clearResults = function () {
  const ctx = elements.outputCanvas.getContext('2d');
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, elements.outputCanvas.width, elements.outputCanvas.height);

  // 重置性能指标显示
  elements.inferenceTime.textContent = '--';
  elements.memoryUsage.textContent = '--';
  elements.fps.textContent = '--';

  showStatus('info', '结果已清除', elements.inferenceStatus);
};

/**
 * 更新按钮状态
 */
function updateButtons() {
  const canInference = isModelLoaded && isImageLoaded;
  elements.inferenceBtn.disabled = !canInference;
  elements.benchmarkBtn.disabled = !canInference;

  if (canInference) {
    showStatus('success', '✅ 准备就绪，可以开始推理', elements.inferenceStatus);
  }
}

/**
 * 显示状态消息
 */
function showStatus(type, message, element = elements.inferenceStatus) {
  element.textContent = message;
  element.className = `status ${type}`;
}

/**
 * 显示进度
 */
function showProgress(percent) {
  elements.progressContainer.style.display = 'block';
  elements.progressFill.style.width = `${percent}%`;
}

/**
 * 隐藏进度
 */
function hideProgress() {
  elements.progressContainer.style.display = 'none';
  elements.progressFill.style.width = '0%';
}

/**
 * 更新性能指标
 */
function updateMetrics(result) {
  const metrics = inference.getPerformanceMetrics();

  elements.inferenceTime.textContent = result.inferenceTime.toFixed(2);
  elements.memoryUsage.textContent = result.memoryUsage.toFixed(2);
  elements.fps.textContent = metrics.fps.toFixed(1);
  elements.totalInferences.textContent = metrics.totalInferences;
}

/**
 * 切换 debug 模式
 */
window.toggleDebugMode = function (enabled) {
  debugMode = enabled;
  console.log(`Debug 模式已${enabled ? '启用' : '禁用'}`);

  // 重新初始化推理库以应用Debug模式
  if (inference) {
    inference.dispose();
    inference = null;
  }

  initializeInference().then(() => {
    showStatus('warning',
      `Debug 模式已${enabled ? '启用' : '禁用'}。\n` +
      `注意：Debug 模式会进行额外的错误检查，可能影响性能。`,
      elements.webglStatus
    );
  });
};

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  if (inference) {
    inference.dispose();
  }
  if (currentStream) {
    currentStream.getTracks().forEach(track => track.stop());
  }
});