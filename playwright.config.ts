import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright配置文件 - WebGL ONNX推理库测试
 * 支持多浏览器测试，包含WebGL兼容性检查
 */
export default defineConfig({
  // 测试目录
  testDir: './tests',
  
  // 并行执行测试
  fullyParallel: true,
  
  // 失败重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并发工作线程数
  workers: process.env.CI ? 1 : undefined,
  
  // 测试报告
  reporter: [
    ['html', { outputFolder: 'playwright-report', open: 'never' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['json', { outputFile: 'test-results/report.json' }]
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: 'http://localhost:5273',
    
    // 浏览器设置
    viewport: { width: 1280, height: 720 },
    
    // 失败时截图
    screenshot: 'only-on-failure',
    
    // 失败时录制视频
    video: 'retain-on-failure',
    
    // 测试追踪
    trace: 'on-first-retry',
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 等待动画结束
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },
  
  // 测试项目配置
  projects: [
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // 启用WebGL相关特性
        launchOptions: {
          args: [
            '--enable-webgl',
            '--enable-accelerated-2d-canvas',
            '--ignore-gpu-blacklist',
            '--disable-web-security',
            '--allow-running-insecure-content'
          ]
        }
      },
    },
  ],
  
  // 测试服务器配置
  webServer: {
    command: 'npm run dev',
    port: 5273,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
  
  // 输出目录
  outputDir: 'test-results/',
  
  // 测试匹配模式
  testMatch: [
    'tests/**/*.test.ts',
    'tests/**/*.spec.ts'
  ],
  
  // 期望超时时间
  expect: {
    timeout: 5000,
  },
  
  // 全局测试超时
  timeout: 30000,
});