<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug 模式性能测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .test-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .result {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }

    .success {
      background: #d4edda;
      color: #155724;
    }

    .info {
      background: #d1ecf1;
      color: #0c5460;
    }

    button {
      padding: 10px 20px;
      margin: 5px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    button:hover {
      background: #0056b3;
    }

    #log {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
    }
  </style>
</head>

<body>
  <h1>🔧 Debug 模式性能测试</h1>

  <div class="test-section">
    <h2>性能对比测试</h2>
    <p>此测试将分别在启用和禁用 debug 模式下创建大量纹理，以验证 debug 模式对性能的影响。</p>

    <button onclick="runPerformanceTest()">运行性能对比测试</button>
    <button onclick="clearLog()">清除日志</button>

    <div id="results"></div>
  </div>

  <div class="test-section">
    <h2>测试日志</h2>
    <div id="log"></div>
  </div>

  <script type="module">
    import { WebGLONNXInference } from './src/index.ts';

    let log = '';

    function logMessage(message) {
      const timestamp = new Date().toLocaleTimeString();
      log += `[${timestamp}] ${message}\n`;
      document.getElementById('log').textContent = log;
      console.log(message);
    }

    function clearLog() {
      log = '';
      document.getElementById('log').textContent = '';
      document.getElementById('results').innerHTML = '';
    }

    async function testTextureCreation(debugMode, iterations = 1000) {
      logMessage(`开始测试 (Debug模式: ${debugMode ? '启用' : '禁用'})`);

      try {
        // 创建推理实例
        const inference = new WebGLONNXInference();
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;

        const config = {
          canvas: canvas,
          precision: 'highp',
          enableOptimizations: true,
          maxTextureSize: 1024,
          batchSize: 1,
          debugMode: debugMode
        };

        const success = await inference.initialize(config);
        if (!success) {
          throw new Error('推理库初始化失败');
        }

        logMessage(`推理库初始化成功，准备创建 ${iterations} 个纹理`);

        // 开始性能测试
        const startTime = performance.now();

        // 模拟创建多个纹理的操作（通过访问上下文管理器）
        const contextManager = inference.contextManager;
        const textures = [];

        for (let i = 0; i < iterations; i++) {
          try {
            const texture = contextManager.createTexture(
              64, 64, // 小纹理以提高测试速度
              contextManager.getContext().RGBA,
              contextManager.getContext().RGBA,
              contextManager.getContext().UNSIGNED_BYTE
            );
            textures.push(texture);

            // 每100个纹理报告一次进度
            if (i > 0 && i % 100 === 0) {
              logMessage(`已创建 ${i} 个纹理...`);
            }
          } catch (error) {
            logMessage(`创建纹理 ${i} 时出错: ${error.message}`);
            break;
          }
        }

        const endTime = performance.now();
        const totalTime = endTime - startTime;

        // 清理纹理
        const gl = contextManager.getContext();
        textures.forEach(texture => gl.deleteTexture(texture));

        // 清理推理实例
        inference.dispose();

        const avgTime = totalTime / textures.length;

        logMessage(`测试完成！`);
        logMessage(`- 总时间: ${totalTime.toFixed(2)}ms`);
        logMessage(`- 成功创建纹理: ${textures.length}个`);
        logMessage(`- 平均每个纹理: ${avgTime.toFixed(4)}ms`);
        logMessage('---');

        return {
          debugMode,
          totalTime,
          textureCount: textures.length,
          avgTime
        };

      } catch (error) {
        logMessage(`测试失败: ${error.message}`);
        return null;
      }
    }

    async function runPerformanceTest() {
      document.getElementById('results').innerHTML = '<div class="result info">正在运行性能测试，请等待...</div>';

      const iterations = 500; // 减少迭代次数以避免浏览器卡顿

      // 测试禁用 debug 模式
      const resultWithoutDebug = await testTextureCreation(false, iterations);

      // 等待一下再测试启用 debug 模式
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 测试启用 debug 模式
      const resultWithDebug = await testTextureCreation(true, iterations);

      // 显示对比结果
      if (resultWithoutDebug && resultWithDebug) {
        const speedup = resultWithDebug.totalTime / resultWithoutDebug.totalTime;
        const percentage = ((speedup - 1) * 100).toFixed(1);

        let resultsHTML = `
                    <div class="result success">
                        <h3>📊 性能对比结果</h3>
                        <p><strong>禁用 Debug 模式:</strong> ${resultWithoutDebug.totalTime.toFixed(2)}ms (平均 ${resultWithoutDebug.avgTime.toFixed(4)}ms/纹理)</p>
                        <p><strong>启用 Debug 模式:</strong> ${resultWithDebug.totalTime.toFixed(2)}ms (平均 ${resultWithDebug.avgTime.toFixed(4)}ms/纹理)</p>
                        <p><strong>性能差异:</strong> Debug 模式比非 Debug 模式慢 ${percentage}% (${speedup.toFixed(2)}x)</p>
                `;

        if (speedup > 1.1) {
          resultsHTML += `<p style="color: #28a745;">✅ Debug 模式确实对性能有显著影响，优化生效！</p>`;
        } else {
          resultsHTML += `<p style="color: #ffc107;">⚠️ 性能差异较小，可能需要更多的测试来显示差异</p>`;
        }

        resultsHTML += '</div>';
        document.getElementById('results').innerHTML = resultsHTML;
      } else {
        document.getElementById('results').innerHTML = '<div class="result error">❌ 测试失败，请检查日志</div>';
      }
    }

    // 暴露函数到全局作用域
    window.runPerformanceTest = runPerformanceTest;
    window.clearLog = clearLog;

    // 页面加载完成后显示初始信息
    logMessage('Debug 模式性能测试页面已加载');
    logMessage('点击 "运行性能对比测试" 开始测试');
  </script>
</body>

</html>