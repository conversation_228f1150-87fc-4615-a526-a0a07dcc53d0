import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'WebGLONNXInference',
      fileName: (format) => `webgl-onnx-inference.${format}.js`,
      formats: ['es', 'umd']
    },
    rollupOptions: {
      external: ['protobufjs'],
      output: {
        globals: {
          'protobufjs': 'protobuf'
        }
      }
    },
    sourcemap: true,
    minify: 'terser'
  },
  server: {
    port: 5173,
    open: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
});