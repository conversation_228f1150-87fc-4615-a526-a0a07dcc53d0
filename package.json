{"name": "webgl-onnx-inference", "version": "1.0.0", "description": "A pure WebGL ONNX model inference library with texture-based input/output", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite --port 5273", "serve": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "rm -rf dist", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:smoke": "playwright test --grep '@smoke'", "test:regression": "playwright test --grep '@regression'", "test:performance": "playwright test --grep '@performance'", "test:report": "playwright show-report", "test:install": "playwright install"}, "devDependencies": {"@playwright/test": "^1.55.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "playwright": "^1.55.0", "prettier": "^3.0.0", "terser": "^5.43.1", "typescript": "^5.0.0", "vite": "^5.0.0"}, "dependencies": {"protobufjs": "^7.2.0"}, "keywords": ["webgl", "onnx", "inference", "machine-learning", "gpu", "texture", "typescript"], "author": "WebGL ONNX Inference Team", "license": "MIT"}