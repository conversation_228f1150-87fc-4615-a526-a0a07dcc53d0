#!/usr/bin/env python3
"""
生成测试所需的图片资源
包含标准测试图片、边界尺寸图片等
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images():
    """创建测试图片"""
    
    # 确保assets目录存在
    assets_dir = "tests/assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    # 1. 标准测试图片 (512x512)
    img = Image.new('RGB', (512, 512), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制渐变背景
    for y in range(512):
        for x in range(512):
            # 创建径向渐变
            center_x, center_y = 256, 256
            distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
            max_distance = (256 ** 2 + 256 ** 2) ** 0.5
            
            # 计算颜色值
            intensity = int(255 * (1 - distance / max_distance))
            color = (intensity, intensity // 2, 255 - intensity)
            img.putpixel((x, y), color)
    
    # 添加几何图形
    draw.rectangle([100, 100, 200, 200], fill=(255, 0, 0), outline=(0, 0, 0), width=3)
    draw.ellipse([300, 100, 400, 200], fill=(0, 255, 0), outline=(0, 0, 0), width=3)
    draw.polygon([(256, 300), (200, 400), (312, 400)], fill=(0, 0, 255), outline=(0, 0, 0))
    
    img.save(f"{assets_dir}/test.png")
    print("✓ 创建标准测试图片: test.png")
    
    # 2. 小尺寸测试图片 (224x224)
    small_img = img.resize((224, 224), Image.Resampling.LANCZOS)
    small_img.save(f"{assets_dir}/test_224.png")
    print("✓ 创建小尺寸测试图片: test_224.png")
    
    # 3. 边界尺寸图片 (1x1)
    edge_img = Image.new('RGB', (1, 1), color=(128, 128, 128))
    edge_img.save(f"{assets_dir}/edge_1x1.png")
    print("✓ 创建边界尺寸图片: edge_1x1.png")
    
    # 4. 大尺寸图片 (1024x1024)
    large_img = img.resize((1024, 1024), Image.Resampling.LANCZOS)
    large_img.save(f"{assets_dir}/large_1024.png")
    print("✓ 创建大尺寸测试图片: large_1024.png")
    
    # 5. 灰度图片
    gray_img = img.convert('L')
    gray_img.save(f"{assets_dir}/test_gray.png")
    print("✓ 创建灰度测试图片: test_gray.png")
    
    # 6. 透明图片 (PNG with alpha)
    rgba_img = Image.new('RGBA', (512, 512), (255, 255, 255, 0))
    draw_rgba = ImageDraw.Draw(rgba_img)
    draw_rgba.ellipse([100, 100, 400, 400], fill=(255, 0, 0, 128))
    rgba_img.save(f"{assets_dir}/test_transparent.png")
    print("✓ 创建透明测试图片: test_transparent.png")
    
    # 7. 纯色测试图片集
    colors = [
        ('red', (255, 0, 0)),
        ('green', (0, 255, 0)),
        ('blue', (0, 0, 255)),
        ('black', (0, 0, 0)),
        ('white', (255, 255, 255))
    ]
    
    for name, color in colors:
        solid_img = Image.new('RGB', (512, 512), color)
        solid_img.save(f"{assets_dir}/solid_{name}.png")
        print(f"✓ 创建纯色测试图片: solid_{name}.png")

def create_test_data():
    """创建测试预期结果数据"""
    
    fixtures_dir = "tests/fixtures"
    os.makedirs(fixtures_dir, exist_ok=True)
    
    # 创建预期结果数据
    expected_results = {
        'test.png': {
            'outputShape': [1, 1, 512, 512],
            'expectedRange': [0, 1],
            'description': '标准测试图片的预期结果'
        },
        'test_224.png': {
            'outputShape': [1, 1, 224, 224],
            'expectedRange': [0, 1],
            'description': '224x224测试图片的预期结果'
        },
        'test_gray.png': {
            'outputShape': [1, 1, 512, 512],
            'expectedRange': [0, 1],
            'description': '灰度图片的预期结果'
        }
    }
    
    # 保存为JSON文件
    import json
    with open(f"{fixtures_dir}/expected_results.json", 'w', encoding='utf-8') as f:
        json.dump(expected_results, f, indent=2, ensure_ascii=False)
    
    print("✓ 创建预期结果数据: expected_results.json")

if __name__ == "__main__":
    print("🎨 开始生成测试图片资源...")
    create_test_images()
    create_test_data()
    print("✅ 测试图片资源生成完成!")