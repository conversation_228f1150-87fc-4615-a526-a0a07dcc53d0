<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WebGL ONNX 推理库快速测试</h1>
        
        <button onclick="runQuickTest()">运行快速测试</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="log" class="log">点击"运行快速测试"开始测试...</div>
    </div>

    <script type="module">
        let logElement = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        window.clearLog = function() {
            logElement.innerHTML = '';
        };
        
        window.runQuickTest = async function() {
            clearLog();
            log('🚀 开始快速测试...', 'info');
            
            try {
                // 1. 检查模型文件是否可访问
                log('1. 检查模型文件...', 'info');
                const response = await fetch('/model.onnx');
                log(`模型文件状态: ${response.status}, 大小: ${response.headers.get('content-length')} bytes`, 'info');
                
                if (!response.ok) {
                    throw new Error('模型文件无法访问');
                }
                
                const arrayBuffer = await response.arrayBuffer();
                log(`✅ 模型文件下载成功: ${arrayBuffer.byteLength} bytes`, 'success');
                
                // 2. 检查推理库是否可以加载
                log('2. 检查推理库...', 'info');
                const module = await import('./dist/webgl-onnx-inference.es.js?v=' + Date.now());
                const { WebGLONNXInference, checkWebGLSupport } = module;
                log('✅ 推理库导入成功', 'success');
                
                // 3. 检查WebGL支持
                log('3. 检查WebGL支持...', 'info');
                const webglSupported = checkWebGLSupport();
                log(`WebGL支持: ${webglSupported}`, webglSupported ? 'success' : 'error');
                
                if (!webglSupported) {
                    throw new Error('WebGL不支持');
                }
                
                // 4. 尝试初始化推理库
                log('4. 初始化推理库...', 'info');
                const inference = new WebGLONNXInference();
                
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 512;
                
                const config = {
                    canvas: canvas,
                    precision: 'highp',
                    enableOptimizations: true,
                    maxTextureSize: 4096,
                    batchSize: 1
                };
                
                const initSuccess = await inference.initialize(config);
                log(`推理库初始化: ${initSuccess}`, initSuccess ? 'success' : 'error');
                
                if (!initSuccess) {
                    throw new Error('推理库初始化失败');
                }
                
                // 5. 尝试加载模型
                log('5. 加载模型...', 'info');
                try {
                    const modelInfo = await inference.loadModel(arrayBuffer);
                    log(`✅ 模型加载成功: ${JSON.stringify(modelInfo, null, 2)}`, 'success');
                } catch (error) {
                    log(`⚠️ 模型加载失败: ${error.message}`, 'warning');
                    log(`错误详情: ${error.stack}`, 'warning');
                    
                    // 尝试单独测试protobuf加载
                    log('尝试单独测试protobuf加载...', 'info');
                    try {
                        const protoResponse = await fetch('/onnx.proto');
                        log(`protobuf文件状态: ${protoResponse.status}`, protoResponse.ok ? 'success' : 'error');
                        
                        if (protoResponse.ok) {
                            const protoText = await protoResponse.text();
                            log(`protobuf文件大小: ${protoText.length} 字符`, 'success');
                            log(`protobuf前100字符: ${protoText.substring(0, 100)}`, 'info');
                        }
                    } catch (protoError) {
                        log(`protobuf加载失败: ${protoError.message}`, 'error');
                    }
                    
                    // 继续测试，不中断
                }
                
                log('🎉 快速测试完成 - 基础功能正常', 'success');
                
            } catch (error) {
                log(`❌ 快速测试失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        };
    </script>
</body>
</html>