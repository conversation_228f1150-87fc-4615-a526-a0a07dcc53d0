# WebGL ONNX 推理库设计文档

## 概述

本项目旨在开发一个纯WebGL的ONNX模型推理库，通过GPU纹理计算实现高性能的神经网络推理，完全避免CPU操作。该库能够接收纹理输入，输出纹理结果，支持在Canvas上渲染灰度图像。

### 核心特性
- 纯WebGL实现，无CPU运算瓶颈
- 基于纹理的输入输出，内存效率高
- 支持ONNX模型格式
- 实时推理能力
- 支持多种操作符的GPU实现

### 技术栈
- WebGL 2.0
- TypeScript (纯TypeScript实现)
- Protocol Buffers (ONNX模型解析)
- Canvas API
- Vite/Webpack (构建工具)
- ESLint + Prettier (代码规范)

## 架构设计

### 整体架构

```mermaid
graph TB
    A[输入纹理] --> B[WebGL上下文]
    B --> C[ONNX模型解析器]
    C --> D[操作符映射器]
    D --> E[着色器生成器]
    E --> F[GPU推理引擎]
    F --> G[输出纹理]
    G --> H[Canvas渲染器]
    
    I[模型文件] --> C
    J[着色器库] --> E
```

### 核心模块设计

#### 1. WebGL上下文管理器 (WebGLContextManager)
- 初始化WebGL上下文
- 管理纹理资源
- 处理扩展支持检测

#### 2. ONNX模型解析器 (ONNXModelParser)
- 解析.onnx文件
- 提取计算图结构
- 解析模型权重和偏置

#### 3. 操作符映射器 (OperatorMapper)
- 将ONNX操作符映射到WebGL实现
- 管理操作符参数
- 处理操作符依赖关系

#### 4. 着色器生成器 (ShaderGenerator)
- 动态生成顶点和片段着色器
- 优化着色器性能
- 处理不同数据类型

#### 5. GPU推理引擎 (GPUInferenceEngine)
- 执行推理计算
- 管理帧缓冲区
- 控制执行流程

#### 6. 纹理管理器 (TextureManager)
- 创建和销毁纹理
- 纹理格式转换
- 内存池管理

## 数据流架构

### 推理流程

```mermaid
sequenceDiagram
    participant Client
    participant Engine
    participant Parser
    participant Shader
    participant GPU
    
    Client->>Engine: loadModel(onnxFile)
    Engine->>Parser: parseModel(onnxFile)
    Parser-->>Engine: graphStructure
    
    Client->>Engine: setInputTexture(texture)
    Engine->>Shader: generateShaders(operations)
    Shader-->>Engine: compiledShaders
    
    Client->>Engine: inference()
    Engine->>GPU: executeShaders()
    GPU-->>Engine: outputTexture
    Engine-->>Client: resultTexture
```

### 纹理数据格式

| 格式 | 用途 | 精度 |
|------|------|------|
| RGBA32F | 权重存储 | 32位浮点 |
| RGBA16F | 中间计算 | 16位浮点 |
| RGBA8 | 输入图像 | 8位整数 |
| R8 | 灰度输出 | 8位整数 |

## 核心组件详细设计

### WebGL上下文管理器

```mermaid
classDiagram
    class WebGLContextManager {
        -gl: WebGL2RenderingContext
        -canvas: HTMLCanvasElement
        -extensions: Map~string, any~
        +initialize(canvas): boolean
        +checkExtensions(): boolean
        +createTexture(width, height, format): WebGLTexture
        +createFramebuffer(): WebGLFramebuffer
        +cleanup(): void
    }
```

**职责:**
- 初始化WebGL2上下文
- 检测必要扩展支持 (EXT_color_buffer_float, OES_texture_float_linear等)
- 管理WebGL资源生命周期

### ONNX模型解析器

```mermaid
classDiagram
    class ONNXModelParser {
        -modelProto: ModelProto
        -graphNodes: NodeProto[]
        -initializers: Map~string, TensorProto~
        +loadModel(buffer): Promise~void~
        +parseGraph(): ComputationGraph
        +extractWeights(): Map~string, Float32Array~
        +getInputSpecs(): InputSpec[]
        +getOutputSpecs(): OutputSpec[]
    }
    
    class ComputationGraph {
        -nodes: OperationNode[]
        -inputs: string[]
        -outputs: string[]
        +getExecutionOrder(): OperationNode[]
        +validateGraph(): boolean
    }
```

**关键功能:**
- 使用Protocol Buffers解析ONNX文件
- 构建计算图依赖关系
- 提取模型权重数据
- 验证模型完整性

### 操作符映射器

```mermaid
classDiagram
    class OperatorMapper {
        -operatorRegistry: Map~string, OperatorImplementation~
        +registerOperator(name, impl): void
        +mapOperation(node): ShaderOperation
        +getSupportedOps(): string[]
        +validateSupport(graph): ValidationResult
    }
    
    class OperatorImplementation {
        <<interface>>
        +generateShader(params): string
        +setupUniforms(gl, program, params): void
        +getOutputShape(inputShapes): number[]
    }
```

**支持的操作符:**
- Conv2D (卷积)
- Relu (激活函数)
- MaxPool2D (最大池化)
- BatchNormalization (批归一化)
- Add, Mul (逐元素运算)
- Reshape (形状变换)
- Softmax (概率分布)

### 着色器生成器

```mermaid
classDiagram
    class ShaderGenerator {
        -vertexShaderTemplate: string
        -fragmentShaderBase: string
        +generateVertexShader(): string
        +generateFragmentShader(operation): string
        +compileShader(gl, source, type): WebGLShader
        +createProgram(gl, vertexShader, fragmentShader): WebGLProgram
        +optimizeShader(source): string
    }
```

**着色器设计原则:**
- 使用纹理采样器读取数据
- 实现向量化计算
- 支持多种精度格式
- 优化内存访问模式

### GPU推理引擎

```mermaid
classDiagram
    class GPUInferenceEngine {
        -gl: WebGL2RenderingContext
        -programs: Map~string, WebGLProgram~
        -textures: Map~string, WebGLTexture~
        -framebuffers: WebGLFramebuffer[]
        +loadModel(modelData): Promise~void~
        +setInput(texture): void
        +execute(): Promise~WebGLTexture~
        +getOutput(): WebGLTexture
        -executeNode(node): void
        -bindTextures(inputs): void
    }
```

**执行策略:**
- 按拓扑顺序执行操作
- 使用帧缓冲区进行中间结果存储
- 实现纹理乒乓技术避免读写冲突
- 支持批处理优化

## 性能优化策略

### 内存优化
- **纹理池化**: 复用相同尺寸的纹理
- **就地计算**: 在可能的情况下重用输入纹理
- **精度优化**: 根据操作类型选择合适的数据精度

### 计算优化
- **操作融合**: 合并连续的简单操作
- **并行化**: 利用GPU的并行特性
- **缓存优化**: 优化纹理访问模式

### 代码示例框架

#### 纹理创建和数据上传
```javascript
// 创建输入纹理
const inputTexture = createTexture(gl, imageData, gl.RGBA, gl.UNSIGNED_BYTE);

// 创建权重纹理
const weightTexture = createTexture(gl, weightData, gl.RGBA32F, gl.FLOAT);
```

#### 着色器执行模式
```glsl
// 卷积操作片段着色器示例
#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_kernel;
uniform vec2 u_inputSize;
uniform vec2 u_kernelSize;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 result = vec4(0.0);
    // 卷积计算逻辑
    for(int i = 0; i < kernelSize.x; i++) {
        for(int j = 0; j < kernelSize.y; j++) {
            // 纹理采样和累加
        }
    }
    fragColor = result;
}
```

## 测试策略

### 单元测试
- **操作符测试**: 验证每个操作符的数学正确性
- **纹理操作测试**: 验证纹理创建、读写操作
- **着色器编译测试**: 确保着色器正确编译

### 集成测试
- **端到端推理测试**: 完整的模型推理流程
- **性能基准测试**: 与CPU实现对比
- **内存泄漏测试**: 长时间运行稳定性

### 测试数据集
- 标准卷积网络模型
- 图像分类任务
- 语义分割模型

## 错误处理

### WebGL错误处理
- 上下文丢失恢复
- 着色器编译错误捕获
- 纹理创建失败处理

### 模型兼容性
- 不支持操作符的友好提示
- 模型格式验证
- 硬件能力检测

## API设计

### 核心API接口

```typescript
// 核心接口定义
interface WebGLONNXInferenceConfig {
  canvas: HTMLCanvasElement;
  precision?: 'highp' | 'mediump' | 'lowp';
  enableOptimizations?: boolean;
  maxTextureSize?: number;
  batchSize?: number;
}

interface InferenceResult {
  outputTexture: WebGLTexture;
  inferenceTime: number;
  memoryUsage: number;
}

interface ModelInfo {
  inputShape: number[];
  outputShape: number[];
  operatorCount: number;
  modelSize: number;
}

// 主接口
interface IWebGLONNXInference {
  // 初始化推理引擎
  initialize(config: WebGLONNXInferenceConfig): Promise<boolean>;
  
  // 加载ONNX模型
  loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo>;
  
  // 设置输入纹理
  setInputTexture(texture: WebGLTexture): void;
  setInputFromImage(image: HTMLImageElement): void;
  setInputFromCanvas(canvas: HTMLCanvasElement): void;
  setInputFromImageData(imageData: ImageData): void;
  
  // 执行推理
  inference(): Promise<InferenceResult>;
  
  // 获取输出
  getOutputTexture(): WebGLTexture | null;
  getOutputAsImageData(): ImageData | null;
  getOutputAsFloat32Array(): Float32Array | null;
  renderToCanvas(canvas: HTMLCanvasElement): void;
  
  // 性能监控
  getPerformanceMetrics(): PerformanceMetrics;
  
  // 资源管理
  dispose(): void;
}

// 性能指标接口
interface PerformanceMetrics {
  averageInferenceTime: number;
  peakMemoryUsage: number;
  texturePoolSize: number;
  totalInferences: number;
  fps: number;
}

// 错误类型
enum InferenceErrorType {
  WEBGL_NOT_SUPPORTED = 'WEBGL_NOT_SUPPORTED',
  EXTENSION_NOT_AVAILABLE = 'EXTENSION_NOT_AVAILABLE',
  MODEL_PARSE_ERROR = 'MODEL_PARSE_ERROR',
  UNSUPPORTED_OPERATOR = 'UNSUPPORTED_OPERATOR',
  TEXTURE_CREATION_FAILED = 'TEXTURE_CREATION_FAILED',
  SHADER_COMPILATION_ERROR = 'SHADER_COMPILATION_ERROR',
  INFERENCE_EXECUTION_ERROR = 'INFERENCE_EXECUTION_ERROR',
  MEMORY_ALLOCATION_ERROR = 'MEMORY_ALLOCATION_ERROR'
}

class InferenceError extends Error {
  constructor(
    public readonly type: InferenceErrorType,
    message: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = 'InferenceError';
  }
}
```

### 使用示例

```typescript
import { WebGLONNXInference, InferenceErrorType } from './webgl-onnx-inference';

// 初始化推理引擎
const canvas = document.getElementById('canvas') as HTMLCanvasElement;
const inference = new WebGLONNXInference();

try {
  // 初始化配置
  const config: WebGLONNXInferenceConfig = {
    canvas,
    precision: 'highp',
    enableOptimizations: true,
    maxTextureSize: 4096,
    batchSize: 1
  };
  
  const initialized = await inference.initialize(config);
  if (!initialized) {
    throw new Error('初始化失败');
  }

  // 加载模型
  const response = await fetch('/public/model.onnx');
  const modelBuffer = await response.arrayBuffer();
  const modelInfo = await inference.loadModel(modelBuffer);
  
  console.log('模型信息:', modelInfo);

  // 加载测试图片
  const img = new Image();
  img.src = '/public/man.png';
  await new Promise<void>((resolve) => {
    img.onload = () => resolve();
  });

  // 设置输入并执行推理
  inference.setInputFromImage(img);
  const result = await inference.inference();
  
  console.log(`推理时间: ${result.inferenceTime}ms`);
  console.log(`内存使用: ${result.memoryUsage}MB`);

  // 渲染灰度图结果
  const outputCanvas = document.getElementById('output') as HTMLCanvasElement;
  inference.renderToCanvas(outputCanvas);
  
  // 获取性能指标
  const metrics = inference.getPerformanceMetrics();
  console.log('性能指标:', metrics);
  
} catch (error) {
  if (error instanceof InferenceError) {
    switch (error.type) {
      case InferenceErrorType.WEBGL_NOT_SUPPORTED:
        console.error('WebGL不支持');
        break;
      case InferenceErrorType.MODEL_PARSE_ERROR:
        console.error('模型解析错误:', error.details);
        break;
      default:
        console.error('推理错误:', error.message);
    }
  } else {
    console.error('未知错误:', error);
  }
} finally {
  // 清理资源
  inference.dispose();
}
```

## 纹理数据处理

### 输入纹理预处理

```mermaid
flowchart LR
    A[原始图像] --> B[归一化 0-1]
    B --> C[尺寸调整]
    C --> D[格式转换]
    D --> E[GPU纹理]
    
    F[预处理参数] --> B
    G[目标尺寸] --> C
    H[通道顺序] --> D
```

**预处理步骤:**
1. **像素值归一化**: 将0-255映射到0-1范围
2. **尺寸标准化**: 调整到模型要求的输入尺寸
3. **通道处理**: RGB到所需通道格式转换
4. **数据布局**: 调整为GPU友好的内存布局

### 纹理格式映射表

| 数据类型 | WebGL格式 | 内部格式 | 精度 | 用途 |
|----------|-----------|----------|------|------|
| 输入图像 | RGBA | RGBA8 | 8位 | 原始图片数据 |
| 模型权重 | RGBA | RGBA32F | 32位 | 高精度权重 |
| 中间结果 | RGBA | RGBA16F | 16位 | 计算中间值 |
| 灰度输出 | RED | R8 | 8位 | 最终输出 |

## 着色器库设计

### 操作符着色器实现

#### 卷积操作着色器
```glsl
#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_kernel;
uniform sampler2D u_bias;
uniform vec2 u_inputSize;
uniform vec2 u_kernelSize;
uniform vec2 u_stride;
uniform vec2 u_padding;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec2 outputPos = v_texCoord * u_inputSize;
    vec4 result = texture(u_bias, vec2(0.0, 0.0));
    
    for(float i = 0.0; i < u_kernelSize.x; i++) {
        for(float j = 0.0; j < u_kernelSize.y; j++) {
            vec2 inputPos = outputPos * u_stride + vec2(i, j) - u_padding;
            vec2 inputCoord = inputPos / u_inputSize;
            
            if(inputCoord.x >= 0.0 && inputCoord.x <= 1.0 && 
               inputCoord.y >= 0.0 && inputCoord.y <= 1.0) {
                vec4 inputValue = texture(u_input, inputCoord);
                vec2 kernelCoord = vec2(i, j) / u_kernelSize;
                vec4 kernelValue = texture(u_kernel, kernelCoord);
                result += inputValue * kernelValue;
            }
        }
    }
    
    fragColor = result;
}
```

#### ReLU激活函数着色器
```glsl
#version 300 es
precision highp float;

uniform sampler2D u_input;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputValue = texture(u_input, v_texCoord);
    fragColor = max(inputValue, 0.0);
}
```

#### 池化操作着色器
```glsl
#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform vec2 u_inputSize;
uniform vec2 u_poolSize;
uniform vec2 u_stride;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec2 startPos = v_texCoord * u_inputSize * u_stride;
    vec4 maxValue = vec4(-1000.0);
    
    for(float i = 0.0; i < u_poolSize.x; i++) {
        for(float j = 0.0; j < u_poolSize.y; j++) {
            vec2 samplePos = (startPos + vec2(i, j)) / u_inputSize;
            if(samplePos.x <= 1.0 && samplePos.y <= 1.0) {
                vec4 value = texture(u_input, samplePos);
                maxValue = max(maxValue, value);
            }
        }
    }
    
    fragColor = maxValue;
}
```

## 内存管理策略

### 纹理池管理

```mermaid
classDiagram
    class TexturePool {
        -availableTextures: Map~string, WebGLTexture[]~
        -usedTextures: Set~WebGLTexture~
        -textureSpecs: Map~WebGLTexture, TextureSpec~
        +acquire(width, height, format): WebGLTexture
        +release(texture): void
        +cleanup(): void
        +getMemoryUsage(): number
    }
    
    class TextureSpec {
        +width: number
        +height: number
        +format: number
        +type: number
        +size: number
    }
```

**池化策略:**
- 按纹理规格分组管理
- 支持动态扩容和收缩
- 自动清理长期未使用的纹理
- 内存使用量监控和告警

### 帧缓冲区管理

```javascript
class FramebufferManager {
    constructor(gl) {
        this.gl = gl;
        this.framebuffers = [];
        this.currentIndex = 0;
    }
    
    // 乒乓缓冲区技术
    getNextFramebuffer() {
        const fb = this.framebuffers[this.currentIndex];
        this.currentIndex = (this.currentIndex + 1) % this.framebuffers.length;
        return fb;
    }
    
    // 绑定输出纹理
    bindOutputTexture(framebuffer, texture) {
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, framebuffer);
        this.gl.framebufferTexture2D(
            this.gl.FRAMEBUFFER, 
            this.gl.COLOR_ATTACHMENT0, 
            this.gl.TEXTURE_2D, 
            texture, 
            0
        );
    }
}
```

## 错误处理和调试

### 错误类型定义

```typescript
enum InferenceErrorType {
    WEBGL_NOT_SUPPORTED = 'WEBGL_NOT_SUPPORTED',
    EXTENSION_NOT_AVAILABLE = 'EXTENSION_NOT_AVAILABLE',
    MODEL_PARSE_ERROR = 'MODEL_PARSE_ERROR',
    UNSUPPORTED_OPERATOR = 'UNSUPPORTED_OPERATOR',
    TEXTURE_CREATION_FAILED = 'TEXTURE_CREATION_FAILED',
    SHADER_COMPILATION_ERROR = 'SHADER_COMPILATION_ERROR',
    INFERENCE_EXECUTION_ERROR = 'INFERENCE_EXECUTION_ERROR',
    MEMORY_ALLOCATION_ERROR = 'MEMORY_ALLOCATION_ERROR'
}

class InferenceError extends Error {
    constructor(
        public type: InferenceErrorType,
        public message: string,
        public details?: any
    ) {
        super(message);
    }
}
```

### 调试工具

```javascript
class DebugTools {
    static captureTexture(gl, texture, width, height) {
        const framebuffer = gl.createFramebuffer();
        gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);
        
        const pixels = new Float32Array(width * height * 4);
        gl.readPixels(0, 0, width, height, gl.RGBA, gl.FLOAT, pixels);
        
        gl.deleteFramebuffer(framebuffer);
        return pixels;
    }
    
    static logShaderInfo(gl, shader) {
        const log = gl.getShaderInfoLog(shader);
        if (log) {
            console.error('Shader compilation error:', log);
        }
    }
    
    static validateFramebuffer(gl) {
        const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);
        return status === gl.FRAMEBUFFER_COMPLETE;
    }
}
```

## 性能监控

### 性能指标收集

```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            inferenceTime: [],
            memoryUsage: [],
            textureCreationTime: [],
            shaderCompilationTime: []
        };
    }
    
    startTimer(name) {
        this.timers = this.timers || {};
        this.timers[name] = performance.now();
    }
    
    endTimer(name) {
        if (this.timers && this.timers[name]) {
            const duration = performance.now() - this.timers[name];
            this.metrics[name] = this.metrics[name] || [];
            this.metrics[name].push(duration);
            delete this.timers[name];
            return duration;
        }
    }
    
    getAverageTime(name) {
        const times = this.metrics[name] || [];
        return times.reduce((a, b) => a + b, 0) / times.length;
    }
}
```

## 部署考虑

### 浏览器兼容性检测

```javascript
class CompatibilityChecker {
    static checkWebGLSupport() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl2');
        return {
            supported: !!gl,
            version: gl ? gl.getParameter(gl.VERSION) : null,
            vendor: gl ? gl.getParameter(gl.VENDOR) : null,
            renderer: gl ? gl.getParameter(gl.RENDERER) : null
        };
    }
    
    static checkRequiredExtensions(gl) {
        const required = [
            'EXT_color_buffer_float',
            'OES_texture_float_linear',
            'EXT_float_blend'
        ];
        
        const available = {};
        required.forEach(ext => {
            available[ext] = !!gl.getExtension(ext);
        });
        
        return available;
    }
    
    static getHardwareLimits(gl) {
        return {
            maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
            maxRenderBufferSize: gl.getParameter(gl.MAX_RENDERBUFFER_SIZE),
            maxTextureUnits: gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS),
            maxVertexTextureUnits: gl.getParameter(gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS)
        };
    }
}
```

### 降级策略

```mermaid
flowchart TD
    A[检测WebGL2支持] --> B{支持?}
    B -->|是| C[检测必要扩展]
    B -->|否| D[降级到WebGL1]
    
    C --> E{扩展完整?}
    E -->|是| F[启用完整功能]
    E -->|否| G[部分功能降级]
    
    D --> H{WebGL1可用?}
    H -->|是| I[基础功能模式]
    H -->|否| J[显示不支持错误]
    
    F --> K[GPU加速推理]
    G --> L[混合CPU/GPU模式]
    I --> M[CPU后备模式]
```

### 资源管理

```javascript
class ResourceManager {
    constructor() {
        this.resources = new Set();
        this.memoryThreshold = 512 * 1024 * 1024; // 512MB
    }
    
    register(resource) {
        this.resources.add(resource);
        this.checkMemoryUsage();
    }
    
    unregister(resource) {
        this.resources.delete(resource);
        if (resource.dispose) {
            resource.dispose();
        }
    }
    
    cleanup() {
        this.resources.forEach(resource => {
            if (resource.dispose) {
                resource.dispose();
            }
        });
        this.resources.clear();
    }
    
    checkMemoryUsage() {
        if (performance.memory) {
            const used = performance.memory.usedJSHeapSize;
            if (used > this.memoryThreshold) {
                console.warn('Memory usage high:', used / 1024 / 1024, 'MB');
                this.triggerGarbageCollection();
            }
        }
    }
    
    triggerGarbageCollection() {
        // 清理未使用的纹理
        // 压缩纹理池
        // 释放临时资源
    }
}

## 高级优化技术

### 操作符融合策略

```mermaid
flowchart LR
    A[卷积层] --> B[批归一化]
    B --> C[ReLU激活]
    
    D[融合后] --> E[单个着色器]
    
    A -.融合.-> D
    B -.融合.-> D
    C -.融合.-> D
```

**融合规则:**
1. **Conv + BN + ReLU** - 最常见的融合模式
2. **Add + ReLU** - 残差连接优化
3. **Mul + Add** - 仿射变换合并
4. **Reshape + Transpose** - 维度操作合并

#### 融合着色器示例
```glsl
// Conv + BatchNorm + ReLU 融合着色器
#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_weight;
uniform sampler2D u_bias;
uniform sampler2D u_bn_scale;
uniform sampler2D u_bn_offset;
uniform float u_bn_epsilon;

in vec2 v_texCoord;
out vec4 fragColor;

vec4 performConvolution(sampler2D input, sampler2D weight, sampler2D bias, vec2 coord) {
    // 卷积计算实现
    return vec4(0.0);
}

void main() {
    // 卷积计算
    vec4 convResult = performConvolution(u_input, u_weight, u_bias, v_texCoord);
    
    // 批归一化
    vec4 scale = texture(u_bn_scale, vec2(0.0, 0.0));
    vec4 offset = texture(u_bn_offset, vec2(0.0, 0.0));
    vec4 normalized = convResult * scale + offset;
    
    // ReLU激活
    fragColor = max(normalized, 0.0);
}
```

### 内存访问优化

#### 纹理分块策略
```javascript
class TileOptimizer {
    constructor(maxTextureSize) {
        this.maxTextureSize = maxTextureSize;
        this.optimalTileSize = this.calculateOptimalTileSize();
    }
    
    calculateOptimalTileSize() {
        // 基于GPU架构优化瓦片大小
        const baseSize = 256;
        const memoryBandwidth = this.estimateMemoryBandwidth();
        return Math.min(baseSize * memoryBandwidth, this.maxTextureSize / 4);
    }
    
    splitLargeTexture(width, height) {
        const tiles = [];
        for (let y = 0; y < height; y += this.optimalTileSize) {
            for (let x = 0; x < width; x += this.optimalTileSize) {
                tiles.push({
                    x, y,
                    width: Math.min(this.optimalTileSize, width - x),
                    height: Math.min(this.optimalTileSize, height - y)
                });
            }
        }
        return tiles;
    }
    
    estimateMemoryBandwidth() {
        // 简化的带宽估算
        return 2; // 默认值
    }
}
```

### 数据精度优化

#### 动态精度选择
```javascript
class PrecisionOptimizer {
    static selectOptimalPrecision(operation, inputRanges) {
        const precisionMap = {
            'conv2d': {
                weights: 'float16',
                activations: 'float16',
                output: 'float16'
            },
            'relu': {
                input: 'float16',
                output: 'float16'
            },
            'softmax': {
                input: 'float32',  // 需要高精度
                output: 'float32'
            }
        };
        
        return precisionMap[operation] || { default: 'float16' };
    }
    
    static quantizeWeights(weights, targetBits = 8) {
        const min = Math.min(...weights);
        const max = Math.max(...weights);
        const scale = (max - min) / ((1 << targetBits) - 1);
        
        return {
            quantized: weights.map(w => Math.round((w - min) / scale)),
            scale,
            offset: min
        };
    }
}
```

## 模型支持和转换

### 支持的ONNX操作符

| 操作符 | 支持状态 | WebGL实现 | 限制 |
|--------|----------|-----------|------|
| Conv | ✅ 完全支持 | 自定义着色器 | - |
| Relu | ✅ 完全支持 | max(x, 0) | - |
| MaxPool | ✅ 完全支持 | 采样比较 | - |
| BatchNormalization | ✅ 完全支持 | 线性变换 | - |
| Add | ✅ 完全支持 | 纹理相加 | 需同尺寸 |
| Mul | ✅ 完全支持 | 纹理相乘 | 需同尺寸 |
| Reshape | ✅ 完全支持 | 坐标重映射 | - |
| Softmax | ✅ 完全支持 | 多pass实现 | 精度要求高 |
| Concat | ✅ 完全支持 | 纹理拼接 | 内存密集 |
| MatMul | ⚠️ 部分支持 | 纹理乘法 | 大矩阵性能差 |
| LSTM | ❌ 计划中 | 循环实现 | 复杂度高 |

### 模型预处理工具

```javascript
class ModelPreprocessor {
    static async optimizeModel(modelBuffer) {
        const model = await this.parseONNX(modelBuffer);
        
        // 1. 操作符融合
        const fusedModel = this.fuseOperators(model);
        
        // 2. 权重量化
        const quantizedModel = this.quantizeWeights(fusedModel);
        
        // 3. 常量折叠
        const foldedModel = this.foldConstants(quantizedModel);
        
        // 4. 死代码消除
        const cleanModel = this.eliminateDeadCode(foldedModel);
        
        return cleanModel;
    }
    
    static fuseOperators(model) {
        const fusionPatterns = [
            ['Conv', 'BatchNormalization', 'Relu'],
            ['Add', 'Relu'],
            ['MatMul', 'Add']
        ];
        
        let modified = true;
        while (modified) {
            modified = false;
            for (const pattern of fusionPatterns) {
                if (this.attemptFusion(model, pattern)) {
                    modified = true;
                }
            }
        }
        
        return model;
    }
    
    static attemptFusion(model, pattern) {
        // 实现操作符融合逻辑
        return false;
    }
}
```

## 实时推理管道

### 流水线执行

```mermaid
gantt
    title WebGL推理流水线
    dateFormat X
    axisFormat %s
    
    section 输入处理
    图像上传     :0, 2
    预处理      :2, 4
    
    section 模型推理
    Layer 1     :4, 6
    Layer 2     :6, 8
    Layer 3     :8, 10
    Layer 4     :10, 12
    
    section 输出处理
    后处理      :12, 14
    结果渲染     :14, 16
```

#### 异步执行管理器
```javascript
class PipelineExecutor {
    constructor(gl) {
        this.gl = gl;
        this.executionQueue = [];
        this.isExecuting = false;
    }
    
    async executeLayer(layer, inputTextures) {
        return new Promise((resolve) => {
            this.executionQueue.push({
                layer,
                inputTextures,
                resolve
            });
            
            if (!this.isExecuting) {
                this.processQueue();
            }
        });
    }
    
    async processQueue() {
        this.isExecuting = true;
        
        while (this.executionQueue.length > 0) {
            const task = this.executionQueue.shift();
            
            // 设置WebGL状态
            this.setupGLState(task.layer);
            
            // 绑定输入纹理
            this.bindInputTextures(task.inputTextures);
            
            // 执行绘制
            this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
            
            // 等待GPU完成
            await this.waitForGPU();
            
            // 获取输出
            const output = this.getOutputTexture();
            task.resolve(output);
        }
        
        this.isExecuting = false;
    }
    
    waitForGPU() {
        return new Promise(resolve => {
            const sync = this.gl.fenceSync(this.gl.SYNC_GPU_COMMANDS_COMPLETE, 0);
            const checkStatus = () => {
                const status = this.gl.clientWaitSync(sync, 0, 0);
                if (status === this.gl.ALREADY_SIGNALED || 
                    status === this.gl.CONDITION_SATISFIED) {
                    this.gl.deleteSync(sync);
                    resolve();
                } else {
                    setTimeout(checkStatus, 1);
                }
            };
            checkStatus();
        });
    }
    
    setupGLState(layer) {
        // 设置WebGL状态
    }
    
    bindInputTextures(textures) {
        // 绑定输入纹理
    }
    
    getOutputTexture() {
        // 获取输出纹理
        return null;
    }
}
```

### 批处理优化

```javascript
class BatchProcessor {
    constructor(maxBatchSize = 4) {
        this.maxBatchSize = maxBatchSize;
        this.pendingInputs = [];
        this.batchTimer = null;
    }
    
    async processBatch(input) {
        return new Promise((resolve, reject) => {
            this.pendingInputs.push({ input, resolve, reject });
            
            if (this.pendingInputs.length >= this.maxBatchSize) {
                this.executeBatch();
            } else if (!this.batchTimer) {
                // 设置超时，避免单个输入等待过久
                this.batchTimer = setTimeout(() => {
                    this.executeBatch();
                }, 16); // ~60fps
            }
        });
    }
    
    async executeBatch() {
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        
        const batch = this.pendingInputs.splice(0, this.maxBatchSize);
        if (batch.length === 0) return;
        
        try {
            // 合并输入纹理
            const batchTexture = this.combineBatchInputs(batch.map(b => b.input));
            
            // 执行批推理
            const batchOutput = await this.inferenceBatch(batchTexture);
            
            // 分离输出结果
            const outputs = this.splitBatchOutputs(batchOutput, batch.length);
            
            // 返回结果
            batch.forEach((item, index) => {
                item.resolve(outputs[index]);
            });
        } catch (error) {
            batch.forEach(item => item.reject(error));
        }
    }
    
    combineBatchInputs(inputs) {
        // 合并多个输入为批次纹理
        return null;
    }
    
    async inferenceBatch(batchTexture) {
        // 执行批次推理
        return null;
    }
    
    splitBatchOutputs(batchOutput, count) {
        // 分离批次输出
        return [];
    }
}
```

## 实际应用场景

### 图像处理应用

#### 实时滤镜
```javascript
class RealtimeFilter {
    constructor(canvas) {
        this.inference = new WebGLONNXInference();
        this.canvas = canvas;
        this.video = null;
        this.animationFrame = null;
    }
    
    async initializeCamera() {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        this.video = document.createElement('video');
        this.video.srcObject = stream;
        this.video.play();
    }
    
    async startRealTimeProcessing() {
        const processFrame = async () => {
            if (this.video.readyState === 4) {
                // 设置视频帧为输入
                this.inference.setInputFromVideo(this.video);
                
                // 执行推理
                const result = await this.inference.inference();
                
                // 渲染结果
                this.inference.renderToCanvas(this.canvas);
            }
            
            this.animationFrame = requestAnimationFrame(processFrame);
        };
        
        processFrame();
    }
    
    stop() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
}
```

#### 批量图像处理
```javascript
class BatchImageProcessor {
    constructor() {
        this.inference = new WebGLONNXInference();
        this.batchProcessor = new BatchProcessor(8);
    }
    
    async processImageSet(imageUrls) {
        const results = await Promise.all(
            imageUrls.map(url => this.processImage(url))
        );
        return results;
    }
    
    async processImage(imageUrl) {
        const img = await this.loadImage(imageUrl);
        return this.batchProcessor.processBatch(img);
    }
    
    loadImage(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = url;
        });
    }
}
```

### 性能基准测试

```javascript
class PerformanceBenchmark {
    constructor() {
        this.testSuites = [];
    }
    
    addTestSuite(name, config) {
        this.testSuites.push({ name, config });
    }
    
    async runBenchmarks() {
        const results = {};
        
        for (const suite of this.testSuites) {
            console.log(`Running benchmark: ${suite.name}`);
            results[suite.name] = await this.runSingleBenchmark(suite.config);
        }
        
        return results;
    }
    
    async runSingleBenchmark(config) {
        const { modelPath, inputSize, iterations = 100 } = config;
        
        // 加载模型
        const inference = new WebGLONNXInference();
        await inference.initialize(document.createElement('canvas'));
        
        const modelBuffer = await fetch(modelPath).then(r => r.arrayBuffer());
        await inference.loadModel(modelBuffer);
        
        // 预热
        const dummyInput = this.createDummyInput(inputSize);
        await inference.setInputTexture(dummyInput);
        await inference.inference();
        
        // 基准测试
        const times = [];
        for (let i = 0; i < iterations; i++) {
            const start = performance.now();
            await inference.inference();
            const end = performance.now();
            times.push(end - start);
        }
        
        return {
            averageTime: times.reduce((a, b) => a + b) / times.length,
            minTime: Math.min(...times),
            maxTime: Math.max(...times),
            fps: 1000 / (times.reduce((a, b) => a + b) / times.length)
        };
    }
    
    createDummyInput(size) {
        // 创建测试输入
        return null;
    }
}
```

## 扩展性设计

### 插件系统

```javascript
class PluginManager {
    constructor() {
        this.plugins = new Map();
        this.hooks = new Map();
    }
    
    registerPlugin(name, plugin) {
        this.plugins.set(name, plugin);
        
        // 注册插件钩子
        if (plugin.hooks) {
            Object.entries(plugin.hooks).forEach(([hookName, handler]) => {
                if (!this.hooks.has(hookName)) {
                    this.hooks.set(hookName, []);
                }
                this.hooks.get(hookName).push(handler);
            });
        }
    }
    
    async executeHook(hookName, ...args) {
        const handlers = this.hooks.get(hookName) || [];
        const results = [];
        
        for (const handler of handlers) {
            const result = await handler(...args);
            results.push(result);
        }
        
        return results;
    }
}

// 自定义操作符插件示例
class CustomOperatorPlugin {
    constructor() {
        this.hooks = {
            'before-inference': this.preprocessInput.bind(this),
            'after-inference': this.postprocessOutput.bind(this)
        };
    }
    
    async preprocessInput(input) {
        // 自定义预处理逻辑
        return input;
    }
    
    async postprocessOutput(output) {
        // 自定义后处理逻辑
        return output;
    }
    
    registerCustomOperator(name, implementation) {
        // 注册新的操作符实现
        // OperatorMapper.registerOperator(name, implementation);
    }
}

## 具体实现细节

### WebGL程序管理

#### 着色器程序缓存
```javascript
class ShaderProgramCache {
    constructor(gl) {
        this.gl = gl;
        this.programs = new Map();
        this.shaderCache = new Map();
    }
    
    getProgram(vertexSource, fragmentSource) {
        const key = this.generateKey(vertexSource, fragmentSource);
        
        if (this.programs.has(key)) {
            return this.programs.get(key);
        }
        
        const program = this.createProgram(vertexSource, fragmentSource);
        this.programs.set(key, program);
        return program;
    }
    
    createProgram(vertexSource, fragmentSource) {
        const vertexShader = this.compileShader(vertexSource, this.gl.VERTEX_SHADER);
        const fragmentShader = this.compileShader(fragmentSource, this.gl.FRAGMENT_SHADER);
        
        const program = this.gl.createProgram();
        this.gl.attachShader(program, vertexShader);
        this.gl.attachShader(program, fragmentShader);
        this.gl.linkProgram(program);
        
        if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
            const error = this.gl.getProgramInfoLog(program);
            throw new Error(`Program link error: ${error}`);
        }
        
        return program;
    }
    
    compileShader(source, type) {
        const key = `${type}_${this.hashString(source)}`;
        
        if (this.shaderCache.has(key)) {
            return this.shaderCache.get(key);
        }
        
        const shader = this.gl.createShader(type);
        this.gl.shaderSource(shader, source);
        this.gl.compileShader(shader);
        
        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
            const error = this.gl.getShaderInfoLog(shader);
            throw new Error(`Shader compile error: ${error}`);
        }
        
        this.shaderCache.set(key, shader);
        return shader;
    }
    
    generateKey(vertexSource, fragmentSource) {
        return `${this.hashString(vertexSource)}_${this.hashString(fragmentSource)}`;
    }
    
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 32位整数
        }
        return hash.toString(36);
    }
}
```

### 高效的矩阵运算着色器

#### 矩阵乘法优化实现
```glsl
// 优化的矩阵乘法着色器
#version 300 es
precision highp float;

uniform sampler2D u_matrixA;
uniform sampler2D u_matrixB;
uniform vec2 u_sizeA; // [rows, cols]
uniform vec2 u_sizeB; // [rows, cols]
uniform int u_tileSize;

in vec2 v_texCoord;
out vec4 fragColor;

// 分块矩阵乘法
vec4 computeMatMulTiled(vec2 coord) {
    vec4 result = vec4(0.0);
    
    float row = coord.y * u_sizeA.x;
    float col = coord.x * u_sizeB.y;
    
    // 分块处理
    int numTiles = int(ceil(u_sizeA.y / float(u_tileSize)));
    
    for (int tile = 0; tile < numTiles; tile++) {
        for (int k = 0; k < u_tileSize; k++) {
            float actualK = float(tile * u_tileSize + k);
            if (actualK >= u_sizeA.y) break;
            
            vec2 coordA = vec2(actualK / u_sizeA.y, row / u_sizeA.x);
            vec2 coordB = vec2(col / u_sizeB.y, actualK / u_sizeB.x);
            
            vec4 valueA = texture(u_matrixA, coordA);
            vec4 valueB = texture(u_matrixB, coordB);
            
            result += valueA * valueB;
        }
    }
    
    return result;
}

void main() {
    fragColor = computeMatMulTiled(v_texCoord);
}
```

#### 高效卷积实现
```glsl
// 分离卷积着色器
#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_kernelX;
uniform sampler2D u_kernelY;
uniform vec2 u_inputSize;
uniform int u_kernelSize;
uniform vec2 u_stride;

in vec2 v_texCoord;
out vec4 fragColor;

// 水平卷积pass
vec4 convolveHorizontal(vec2 coord) {
    vec4 result = vec4(0.0);
    float kernelRadius = float(u_kernelSize - 1) / 2.0;
    
    for (int i = 0; i < u_kernelSize; i++) {
        float offset = float(i) - kernelRadius;
        vec2 sampleCoord = coord + vec2(offset / u_inputSize.x, 0.0);
        
        if (sampleCoord.x >= 0.0 && sampleCoord.x <= 1.0) {
            vec4 inputValue = texture(u_input, sampleCoord);
            float kernelValue = texture(u_kernelX, vec2(float(i) / float(u_kernelSize - 1), 0.0)).r;
            result += inputValue * kernelValue;
        }
    }
    
    return result;
}

// 垂直卷积pass
vec4 convolveVertical(vec2 coord) {
    vec4 result = vec4(0.0);
    float kernelRadius = float(u_kernelSize - 1) / 2.0;
    
    for (int i = 0; i < u_kernelSize; i++) {
        float offset = float(i) - kernelRadius;
        vec2 sampleCoord = coord + vec2(0.0, offset / u_inputSize.y);
        
        if (sampleCoord.y >= 0.0 && sampleCoord.y <= 1.0) {
            vec4 inputValue = texture(u_input, sampleCoord);
            float kernelValue = texture(u_kernelY, vec2(float(i) / float(u_kernelSize - 1), 0.0)).r;
            result += inputValue * kernelValue;
        }
    }
    
    return result;
}

void main() {
    #ifdef HORIZONTAL_PASS
        fragColor = convolveHorizontal(v_texCoord);
    #else
        fragColor = convolveVertical(v_texCoord);
    #endif
}
```

### 内存管理优化

#### 智能纹理回收
```javascript
class SmartTexturePool {
    constructor(gl) {
        this.gl = gl;
        this.pools = new Map(); // 按尺寸分组
        this.usageStats = new Map();
        this.maxPoolSize = 50;
        this.cleanupThreshold = 100; // 清理阈值
    }
    
    acquire(width, height, format, type) {
        const key = this.getPoolKey(width, height, format, type);
        
        if (!this.pools.has(key)) {
            this.pools.set(key, []);
        }
        
        const pool = this.pools.get(key);
        
        if (pool.length > 0) {
            const texture = pool.pop();
            this.updateUsageStats(key, 'reused');
            return texture;
        }
        
        // 创建新纹理
        const texture = this.createTexture(width, height, format, type);
        this.updateUsageStats(key, 'created');
        return texture;
    }
    
    release(texture, width, height, format, type) {
        const key = this.getPoolKey(width, height, format, type);
        const pool = this.pools.get(key) || [];
        
        if (pool.length < this.maxPoolSize) {
            pool.push(texture);
            this.pools.set(key, pool);
        } else {
            // 池已满，直接删除
            this.gl.deleteTexture(texture);
        }
        
        this.maybeCleanup();
    }
    
    maybeCleanup() {
        const totalTextures = Array.from(this.pools.values())
            .reduce((sum, pool) => sum + pool.length, 0);
            
        if (totalTextures > this.cleanupThreshold) {
            this.cleanup();
        }
    }
    
    cleanup() {
        // 基于使用频率清理纹理
        const sortedPools = Array.from(this.usageStats.entries())
            .sort((a, b) => a[1].reused - b[1].reused);
            
        let cleaned = 0;
        const targetCleanup = Math.floor(this.cleanupThreshold * 0.3);
        
        for (const [key, stats] of sortedPools) {
            if (cleaned >= targetCleanup) break;
            
            const pool = this.pools.get(key);
            if (pool && pool.length > 0) {
                const toRemove = Math.min(pool.length, targetCleanup - cleaned);
                const removed = pool.splice(0, toRemove);
                
                removed.forEach(texture => this.gl.deleteTexture(texture));
                cleaned += toRemove;
            }
        }
    }
    
    getPoolKey(width, height, format, type) {
        return `${width}x${height}_${format}_${type}`;
    }
    
    updateUsageStats(key, action) {
        if (!this.usageStats.has(key)) {
            this.usageStats.set(key, { created: 0, reused: 0 });
        }
        this.usageStats.get(key)[action]++;
    }
    
    createTexture(width, height, format, type) {
        const texture = this.gl.createTexture();
        this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
        this.gl.texImage2D(
            this.gl.TEXTURE_2D, 0, format,
            width, height, 0, format, type, null
        );
        
        // 设置纹理参数
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
        
        return texture;
    }
}
```

### 自适应质量控制

#### 动态LOD系统
```javascript
class AdaptiveQualityController {
    constructor() {
        this.targetFPS = 30;
        this.qualityLevels = [
            { scale: 0.5, precision: 'mediump' },
            { scale: 0.75, precision: 'mediump' },
            { scale: 1.0, precision: 'highp' },
            { scale: 1.0, precision: 'highp' }
        ];
        this.currentLevel = 2;
        this.frameTimeHistory = [];
        this.adjustmentCooldown = 0;
    }
    
    updateFrameTime(frameTime) {
        this.frameTimeHistory.push(frameTime);
        if (this.frameTimeHistory.length > 10) {
            this.frameTimeHistory.shift();
        }
        
        if (this.adjustmentCooldown > 0) {
            this.adjustmentCooldown--;
            return;
        }
        
        const avgFrameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
        const currentFPS = 1000 / avgFrameTime;
        
        if (currentFPS < this.targetFPS * 0.8) {
            this.decreaseQuality();
        } else if (currentFPS > this.targetFPS * 1.2) {
            this.increaseQuality();
        }
    }
    
    decreaseQuality() {
        if (this.currentLevel > 0) {
            this.currentLevel--;
            this.adjustmentCooldown = 30; // 30帧后再调整
            console.log(`Quality decreased to level ${this.currentLevel}`);
        }
    }
    
    increaseQuality() {
        if (this.currentLevel < this.qualityLevels.length - 1) {
            this.currentLevel++;
            this.adjustmentCooldown = 30;
            console.log(`Quality increased to level ${this.currentLevel}`);
        }
    }
    
    getCurrentSettings() {
        return this.qualityLevels[this.currentLevel];
    }
}
```

## 实际部署指南

### 项目结构建议

```
webgl-onnx-inference/
├── src/
│   ├── core/
│   │   ├── WebGLContext.js
│   │   ├── TextureManager.js
│   │   └── ShaderManager.js
│   ├── operators/
│   │   ├── Conv2D.js
│   │   ├── ReLU.js
│   │   └── MaxPool.js
│   ├── parsers/
│   │   └── ONNXParser.js
│   ├── optimizers/
│   │   ├── OperatorFusion.js
│   │   └── MemoryOptimizer.js
│   ├── utils/
│   │   ├── ImageUtils.js
│   │   └── MathUtils.js
│   └── index.js
├── shaders/
│   ├── vertex/
│   │   └── basic.vert
│   └── fragment/
│       ├── conv2d.frag
│       ├── relu.frag
│       └── pool.frag
├── examples/
│   ├── basic-inference/
│   ├── real-time-filter/
│   └── batch-processing/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
└── docs/
    ├── api.md
    ├── examples.md
    └── troubleshooting.md
```

### 构建配置

#### Webpack配置示例
```javascript
const path = require('path');

module.exports = {
    entry: './src/index.js',
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'webgl-onnx-inference.js',
        library: 'WebGLONNXInference',
        libraryTarget: 'umd'
    },
    module: {
        rules: [
            {
                test: /\.(vert|frag)$/,
                use: 'raw-loader'
            },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            }
        ]
    },
    resolve: {
        extensions: ['.js', '.vert', '.frag']
    }
};
```

### 性能监控和调试

#### 实时性能面板
```javascript
class PerformancePanel {
    constructor(container) {
        this.container = container;
        this.metrics = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            textureCount: 0
        };
        this.createPanel();
    }
    
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            border-radius: 5px;
            z-index: 9999;
        `;
        
        this.container.appendChild(this.panel);
        this.updatePanel();
    }
    
    updateMetrics(newMetrics) {
        Object.assign(this.metrics, newMetrics);
        this.updatePanel();
    }
    
    updatePanel() {
        this.panel.innerHTML = `
            <div>FPS: ${this.metrics.fps.toFixed(1)}</div>
            <div>Frame Time: ${this.metrics.frameTime.toFixed(2)}ms</div>
            <div>Memory: ${(this.metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
            <div>Textures: ${this.metrics.textureCount}</div>
        `;
    }
}
```

### 生产环境优化

#### 代码分割和懒加载
```javascript
class LazyOperatorLoader {
    constructor() {
        this.loadedOperators = new Set();
        this.operatorModules = {
            'Conv': () => import('./operators/Conv2D.js'),
            'Relu': () => import('./operators/ReLU.js'),
            'MaxPool': () => import('./operators/MaxPool.js'),
            'BatchNormalization': () => import('./operators/BatchNorm.js')
        };
    }
    
    async loadOperator(operatorType) {
        if (this.loadedOperators.has(operatorType)) {
            return;
        }
        
        if (this.operatorModules[operatorType]) {
            const module = await this.operatorModules[operatorType]();
            // 注册操作符
            OperatorRegistry.register(operatorType, module.default);
            this.loadedOperators.add(operatorType);
        }
    }
    
    async loadRequiredOperators(modelGraph) {
        const requiredOps = new Set();
        
        modelGraph.nodes.forEach(node => {
            requiredOps.add(node.op_type);
        });
        
        const loadPromises = Array.from(requiredOps).map(op => 
            this.loadOperator(op)
        );
        
        await Promise.all(loadPromises);
    }
}
```

#### 缓存策略
```javascript
class ModelCache {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 100 * 1024 * 1024; // 100MB
        this.currentCacheSize = 0;
    }
    
    async getModel(modelUrl) {
        const cacheKey = this.getCacheKey(modelUrl);
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey).data;
        }
        
        const modelData = await this.fetchModel(modelUrl);
        this.cacheModel(cacheKey, modelData);
        
        return modelData;
    }
    
    cacheModel(key, data) {
        const size = data.byteLength;
        
        // 检查缓存空间
        while (this.currentCacheSize + size > this.maxCacheSize && this.cache.size > 0) {
            this.evictLRU();
        }
        
        this.cache.set(key, {
            data,
            size,
            timestamp: Date.now()
        });
        
        this.currentCacheSize += size;
    }
    
    evictLRU() {
        let oldestKey = null;
        let oldestTime = Infinity;
        
        for (const [key, value] of this.cache.entries()) {
            if (value.timestamp < oldestTime) {
                oldestTime = value.timestamp;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            const removed = this.cache.get(oldestKey);
            this.cache.delete(oldestKey);
            this.currentCacheSize -= removed.size;
        }
    }
    
    getCacheKey(url) {
        return btoa(url).replace(/[^a-zA-Z0-9]/g, '');
    }
    
    async fetchModel(url) {
        const response = await fetch(url);
        return await response.arrayBuffer();
    }
}

## TypeScript实现代码

### 项目结构 (TypeScript版本)

```
webgl-onnx-inference/
├── src/
│   ├── core/
│   │   ├── WebGLContextManager.ts
│   │   ├── TextureManager.ts
│   │   ├── ShaderManager.ts
│   │   └── InferenceEngine.ts
│   ├── operators/
│   │   ├── BaseOperator.ts
│   │   ├── Conv2D.ts
│   │   ├── ReLU.ts
│   │   └── MaxPool.ts
│   ├── parsers/
│   │   └── ONNXParser.ts
│   ├── utils/
│   │   ├── ImageUtils.ts
│   │   ├── PerformanceMonitor.ts
│   │   └── Errors.ts
│   ├── types/
│   │   ├── interfaces.ts
│   │   └── onnx.ts
│   └── index.ts
├── examples/
│   ├── basic-inference.html
│   └── real-time-filter.html
├── dist/
├── tests/
├── tsconfig.json
├── package.json
└── vite.config.ts
```

### 主入口文件 (src/index.ts)

```typescript
import { WebGLContextManager } from './core/WebGLContextManager';
import { TextureManager } from './core/TextureManager';
import { ShaderManager } from './core/ShaderManager';
import { ONNXParser } from './parsers/ONNXParser';
import { InferenceEngine } from './core/InferenceEngine';
import { ImageUtils } from './utils/ImageUtils';
import { PerformanceMonitor } from './utils/PerformanceMonitor';
import { InferenceError, InferenceErrorType } from './utils/Errors';
import type {
  IWebGLONNXInference,
  WebGLONNXInferenceConfig,
  InferenceResult,
  ModelInfo,
  PerformanceMetrics
} from './types/interfaces';

export class WebGLONNXInference implements IWebGLONNXInference {
  private gl: WebGL2RenderingContext | null = null;
  private contextManager: WebGLContextManager | null = null;
  private textureManager: TextureManager | null = null;
  private shaderManager: ShaderManager | null = null;
  private parser: ONNXParser | null = null;
  private engine: InferenceEngine | null = null;
  private performanceMonitor: PerformanceMonitor | null = null;
  private model: any = null;
  private isInitialized = false;

  async initialize(config: WebGLONNXInferenceConfig): Promise<boolean> {
    try {
      this.contextManager = new WebGLContextManager();
      this.gl = await this.contextManager.initialize(config.canvas, {
        precision: config.precision || 'highp',
        maxTextureSize: config.maxTextureSize || 4096
      });
      
      if (!this.gl) {
        throw new InferenceError(
          InferenceErrorType.WEBGL_NOT_SUPPORTED,
          'Failed to initialize WebGL2 context'
        );
      }

      this.textureManager = new TextureManager(this.gl);
      this.shaderManager = new ShaderManager(this.gl);
      this.parser = new ONNXParser();
      this.performanceMonitor = new PerformanceMonitor();
      this.engine = new InferenceEngine(
        this.gl,
        this.textureManager,
        this.shaderManager,
        this.performanceMonitor
      );

      this.isInitialized = true;
      console.log('WebGL ONNX Inference initialized successfully');
      return true;
    } catch (error) {
      console.error('Initialization failed:', error);
      return false;
    }
  }

  async loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo> {
    this.checkInitialized();
    
    try {
      this.model = await this.parser!.parseModel(modelBuffer);
      await this.engine!.setModel(this.model);
      
      return {
        inputShape: this.extractShape(this.model.graph.input[0]),
        outputShape: this.extractShape(this.model.graph.output[0]),
        operatorCount: this.model.graph.node.length,
        modelSize: modelBuffer.byteLength
      };
    } catch (error) {
      throw new InferenceError(
        InferenceErrorType.MODEL_PARSE_ERROR,
        'Failed to load model',
        error
      );
    }
  }

  setInputFromImage(image: HTMLImageElement): void {
    this.checkModelLoaded();
    const inputTexture = ImageUtils.imageToTexture(this.gl!, image);
    this.engine!.setInputTexture(inputTexture);
  }

  setInputFromCanvas(canvas: HTMLCanvasElement): void {
    this.checkModelLoaded();
    const inputTexture = ImageUtils.canvasToTexture(this.gl!, canvas);
    this.engine!.setInputTexture(inputTexture);
  }

  setInputFromImageData(imageData: ImageData): void {
    this.checkModelLoaded();
    const inputTexture = ImageUtils.imageDataToTexture(this.gl!, imageData);
    this.engine!.setInputTexture(inputTexture);
  }

  setInputTexture(texture: WebGLTexture): void {
    this.checkModelLoaded();
    this.engine!.setInputTexture(texture);
  }

  async inference(): Promise<InferenceResult> {
    this.checkModelLoaded();
    
    const startTime = performance.now();
    const outputTexture = await this.engine!.execute();
    const inferenceTime = performance.now() - startTime;
    
    this.performanceMonitor!.recordInference(inferenceTime);
    
    return {
      outputTexture,
      inferenceTime,
      memoryUsage: this.textureManager!.getMemoryUsage()
    };
  }

  getOutputTexture(): WebGLTexture | null {
    return this.engine?.getOutputTexture() || null;
  }

  getOutputAsImageData(): ImageData | null {
    const outputTexture = this.getOutputTexture();
    if (!outputTexture) return null;
    
    return ImageUtils.textureToImageData(this.gl!, outputTexture);
  }

  getOutputAsFloat32Array(): Float32Array | null {
    const outputTexture = this.getOutputTexture();
    if (!outputTexture) return null;
    
    return ImageUtils.textureToFloat32Array(this.gl!, outputTexture);
  }

  renderToCanvas(canvas: HTMLCanvasElement): void {
    const outputTexture = this.getOutputTexture();
    if (!outputTexture) {
      throw new InferenceError(
        InferenceErrorType.INFERENCE_EXECUTION_ERROR,
        'No output texture available'
      );
    }
    
    ImageUtils.renderTextureToCanvas(this.gl!, outputTexture, canvas);
  }

  getPerformanceMetrics(): PerformanceMetrics {
    return this.performanceMonitor?.getMetrics() || {
      averageInferenceTime: 0,
      peakMemoryUsage: 0,
      texturePoolSize: 0,
      totalInferences: 0,
      fps: 0
    };
  }

  dispose(): void {
    this.engine?.dispose();
    this.textureManager?.dispose();
    this.shaderManager?.dispose();
    this.performanceMonitor?.dispose();
    
    this.isInitialized = false;
    console.log('WebGL ONNX Inference disposed');
  }

  private checkInitialized(): void {
    if (!this.isInitialized) {
      throw new InferenceError(
        InferenceErrorType.INFERENCE_EXECUTION_ERROR,
        'Engine not initialized. Call initialize() first.'
      );
    }
  }

  private checkModelLoaded(): void {
    this.checkInitialized();
    if (!this.model) {
      throw new InferenceError(
        InferenceErrorType.INFERENCE_EXECUTION_ERROR,
        'Model not loaded. Call loadModel() first.'
      );
    }
  }

  private extractShape(valueInfo: any): number[] {
    return valueInfo.type.tensorType.shape.dim.map((d: any) => 
      d.dimValue || d.dimParam ? parseInt(d.dimParam) || d.dimValue : -1
    );
  }
}

export { InferenceErrorType, InferenceError };
export type { 
  WebGLONNXInferenceConfig, 
  InferenceResult, 
  ModelInfo, 
  PerformanceMetrics 
};
export default WebGLONNXInference;
```

### 类型定义文件 (src/types/interfaces.ts)

```typescript
// 核心接口定义
export interface WebGLONNXInferenceConfig {
  canvas: HTMLCanvasElement;
  precision?: 'highp' | 'mediump' | 'lowp';
  enableOptimizations?: boolean;
  maxTextureSize?: number;
  batchSize?: number;
}

export interface InferenceResult {
  outputTexture: WebGLTexture;
  inferenceTime: number;
  memoryUsage: number;
}

export interface ModelInfo {
  inputShape: number[];
  outputShape: number[];
  operatorCount: number;
  modelSize: number;
}

export interface PerformanceMetrics {
  averageInferenceTime: number;
  peakMemoryUsage: number;
  texturePoolSize: number;
  totalInferences: number;
  fps: number;
}

export interface IWebGLONNXInference {
  initialize(config: WebGLONNXInferenceConfig): Promise<boolean>;
  loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo>;
  setInputTexture(texture: WebGLTexture): void;
  setInputFromImage(image: HTMLImageElement): void;
  setInputFromCanvas(canvas: HTMLCanvasElement): void;
  setInputFromImageData(imageData: ImageData): void;
  inference(): Promise<InferenceResult>;
  getOutputTexture(): WebGLTexture | null;
  getOutputAsImageData(): ImageData | null;
  getOutputAsFloat32Array(): Float32Array | null;
  renderToCanvas(canvas: HTMLCanvasElement): void;
  getPerformanceMetrics(): PerformanceMetrics;
  dispose(): void;
}

// WebGL相关接口
export interface WebGLContextConfig {
  precision: 'highp' | 'mediump' | 'lowp';
  maxTextureSize: number;
  enableExtensions?: string[];
}

export interface TextureSpec {
  width: number;
  height: number;
  format: number;
  type: number;
  internalFormat: number;
}

export interface ShaderConfig {
  vertexSource: string;
  fragmentSource: string;
  uniforms?: Record<string, any>;
  attributes?: Record<string, number>;
}
```

### TypeScript配置文件 (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "removeComments": false,
    "noEmitOnError": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "resolveJsonModule": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "tests"
  ]
}
```

### Vite配置文件 (vite.config.ts)

```typescript
import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'WebGLONNXInference',
      fileName: (format) => `webgl-onnx-inference.${format}.js`,
      formats: ['es', 'umd']
    },
    rollupOptions: {
      external: [],
      output: {
        globals: {}
      }
    },
    target: 'es2020',
    minify: 'terser',
    sourcemap: true
  },
  server: {
    port: 3000,
    open: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
});
```

### 示例页面 (examples/basic-inference.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL ONNX推理示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            max-width: 100%;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .metrics {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>WebGL ONNX推理库示例</h1>
    
    <div class="controls">
        <input type="file" id="modelFile" accept=".onnx" />
        <input type="file" id="imageFile" accept="image/*" />
        <button id="loadModel" disabled>加载模型</button>
        <button id="runInference" disabled>执行推理</button>
    </div>
    
    <div class="container">
        <div>
            <h3>输入图像</h3>
            <canvas id="inputCanvas" width="512" height="512"></canvas>
        </div>
        <div>
            <h3>输出结果</h3>
            <canvas id="outputCanvas" width="512" height="512"></canvas>
        </div>
    </div>
    
    <div class="metrics" id="metrics">
        <h3>性能指标</h3>
        <div id="metricsContent">请先执行推理...</div>
    </div>

    <script type="module">
        import { WebGLONNXInference, InferenceErrorType } from '../dist/webgl-onnx-inference.es.js';
        
        let inference = null;
        let modelLoaded = false;
        let imageLoaded = false;
        
        const modelFileInput = document.getElementById('modelFile');
        const imageFileInput = document.getElementById('imageFile');
        const loadModelBtn = document.getElementById('loadModel');
        const runInferenceBtn = document.getElementById('runInference');
        const inputCanvas = document.getElementById('inputCanvas');
        const outputCanvas = document.getElementById('outputCanvas');
        const metricsContent = document.getElementById('metricsContent');
        
        // 初始化推理引擎
        async function initInference() {
            try {
                inference = new WebGLONNXInference();
                const config = {
                    canvas: inputCanvas,
                    precision: 'highp',
                    enableOptimizations: true,
                    maxTextureSize: 4096
                };
                
                const success = await inference.initialize(config);
                if (success) {
                    console.log('推理引擎初始化成功');
                } else {
                    throw new Error('推理引擎初始化失败');
                }
            } catch (error) {
                console.error('初始化错误:', error);
                alert('初始化失败: ' + error.message);
            }
        }
        
        // 加载模型
        async function loadModel() {
            const file = modelFileInput.files[0];
            if (!file) return;
            
            try {
                loadModelBtn.disabled = true;
                loadModelBtn.textContent = '加载中...';
                
                const arrayBuffer = await file.arrayBuffer();
                const modelInfo = await inference.loadModel(arrayBuffer);
                
                console.log('模型信息:', modelInfo);
                modelLoaded = true;
                updateButtons();
                
                loadModelBtn.textContent = '模型已加载';
            } catch (error) {
                console.error('模型加载错误:', error);
                alert('模型加载失败: ' + error.message);
                loadModelBtn.disabled = false;
                loadModelBtn.textContent = '加载模型';
            }
        }
        
        // 加载图像
        function loadImage() {
            const file = imageFileInput.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    const ctx = inputCanvas.getContext('2d');
                    ctx.clearRect(0, 0, inputCanvas.width, inputCanvas.height);
                    ctx.drawImage(img, 0, 0, inputCanvas.width, inputCanvas.height);
                    
                    imageLoaded = true;
                    updateButtons();
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        // 执行推理
        async function runInference() {
            if (!modelLoaded || !imageLoaded) return;
            
            try {
                runInferenceBtn.disabled = true;
                runInferenceBtn.textContent = '推理中...';
                
                // 设置输入
                inference.setInputFromCanvas(inputCanvas);
                
                // 执行推理
                const result = await inference.inference();
                
                // 渲染输出
                inference.renderToCanvas(outputCanvas);
                
                // 显示指标
                const metrics = inference.getPerformanceMetrics();
                metricsContent.innerHTML = `
                    <p>推理时间: ${result.inferenceTime.toFixed(2)}ms</p>
                    <p>内存使用: ${(result.memoryUsage / 1024 / 1024).toFixed(2)}MB</p>
                    <p>平均推理时间: ${metrics.averageInferenceTime.toFixed(2)}ms</p>
                    <p>推理次数: ${metrics.totalInferences}</p>
                    <p>FPS: ${metrics.fps.toFixed(1)}</p>
                `;
                
                runInferenceBtn.textContent = '执行推理';
                runInferenceBtn.disabled = false;
            } catch (error) {
                console.error('推理错误:', error);
                alert('推理失败: ' + error.message);
                runInferenceBtn.textContent = '执行推理';
                runInferenceBtn.disabled = false;
            }
        }
        
        function updateButtons() {
            loadModelBtn.disabled = !modelFileInput.files[0];
            runInferenceBtn.disabled = !modelLoaded || !imageLoaded;
        }
        
        // 事件监听
        modelFileInput.addEventListener('change', updateButtons);
        imageFileInput.addEventListener('change', loadImage);
        loadModelBtn.addEventListener('click', loadModel);
        runInferenceBtn.addEventListener('click', runInference);
        
        // 初始化
        initInference();
    </script>
</body>
</html>
```
```