<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动Sub操作符测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        #log {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Sub操作符手动测试</h1>
        <p>此页面直接测试Sub操作符是否正确实现。</p>
        
        <div id="status" class="status info">准备开始测试...</div>
        
        <button onclick="testSubOperator()" id="test-btn">开始测试Sub操作符</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="log"></div>
    </div>

    <script type="module">
        // 直接导入源码而不是构建版本
        import { ShaderGenerator } from './src/shaders/ShaderGenerator.js';
        import { WebGLContextManager } from './src/webgl/WebGLContextManager.js';
        
        let log = document.getElementById('log');
        let status = document.getElementById('status');
        
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `[${time}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        window.clearLog = function() {
            log.innerHTML = '';
        }
        
        window.testSubOperator = async function() {
            updateStatus('正在测试Sub操作符...', 'info');
            addLog('🚀 开始Sub操作符测试');
            
            try {
                // 创建WebGL上下文
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 512;
                
                const contextManager = new WebGLContextManager();
                const success = contextManager.initialize(canvas);
                
                if (!success) {
                    throw new Error('WebGL上下文初始化失败');
                }
                addLog('✅ WebGL上下文初始化成功');
                
                // 创建着色器生成器
                const shaderGenerator = new ShaderGenerator(contextManager);
                addLog('✅ 着色器生成器创建成功');
                
                // 测试Sub着色器生成
                try {
                    const vertexShader = shaderGenerator.generateVertexShader();
                    addLog('✅ 顶点着色器生成成功');
                    
                    const fragmentShader = shaderGenerator.generateFragmentShader('sub');
                    addLog('✅ Sub片段着色器生成成功');
                    addLog(`Sub着色器内容片段: ${fragmentShader.substring(0, 100)}...`);
                    
                    // 检查着色器是否包含减法操作
                    if (fragmentShader.includes('input - bias')) {
                        addLog('✅ Sub着色器包含正确的减法操作');
                    } else {
                        throw new Error('Sub着色器不包含减法操作');
                    }
                    
                    // 编译着色器
                    const gl = contextManager.getContext();
                    
                    const vs = shaderGenerator.compileShader(gl, vertexShader, gl.VERTEX_SHADER);
                    addLog('✅ 顶点着色器编译成功');
                    
                    const fs = shaderGenerator.compileShader(gl, fragmentShader, gl.FRAGMENT_SHADER);
                    addLog('✅ Sub片段着色器编译成功');
                    
                    // 创建着色器程序
                    const program = shaderGenerator.createProgram(gl, vs, fs);
                    addLog('✅ Sub着色器程序创建成功');
                    
                    updateStatus('🎉 Sub操作符测试全部通过！', 'success');
                    addLog('🎉 Sub操作符测试完成 - 所有测试通过');
                    
                } catch (shaderError) {
                    throw new Error(`着色器测试失败: ${shaderError.message}`);
                }
                
            } catch (error) {
                addLog(`❌ 测试失败: ${error.message}`);
                updateStatus(`测试失败: ${error.message}`, 'error');
                console.error('Sub操作符测试失败:', error);
            }
        }
        
        // 页面加载完成后的初始化
        addLog('页面加载完成，准备测试Sub操作符');
        updateStatus('页面已加载，点击按钮开始测试', 'info');
    </script>
</body>
</html>