import { InferenceErrorType } from '../types/index.js';

/**
 * 推理错误类
 */
export class InferenceError extends Error {
  public readonly type: InferenceErrorType;
  public readonly details?: unknown;

  constructor(
    type: InferenceErrorType,
    message: string,
    details?: unknown
  ) {
    super(message);
    this.name = 'InferenceError';
    this.type = type;
    this.details = details;
    
    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, InferenceError);
    }
  }

  /**
   * 创建WebGL不支持错误
   */
  static webglNotSupported(details?: unknown): InferenceError {
    return new InferenceError(
      InferenceErrorType.WEBGL_NOT_SUPPORTED,
      'WebGL 2.0 is not supported in this browser',
      details
    );
  }

  /**
   * 创建扩展不可用错误
   */
  static extensionNotAvailable(extensionName: string): InferenceError {
    return new InferenceError(
      InferenceErrorType.EXTENSION_NOT_AVAILABLE,
      `Required WebGL extension '${extensionName}' is not available`,
      { extensionName }
    );
  }

  /**
   * 创建模型解析错误
   */
  static modelParseError(message: string, details?: unknown): InferenceError {
    return new InferenceError(
      InferenceErrorType.MODEL_PARSE_ERROR,
      `Model parse error: ${message}`,
      details
    );
  }

  /**
   * 创建不支持操作符错误
   */
  static unsupportedOperator(operatorType: string): InferenceError {
    return new InferenceError(
      InferenceErrorType.UNSUPPORTED_OPERATOR,
      `Unsupported operator: ${operatorType}`,
      { operatorType }
    );
  }

  /**
   * 创建纹理创建失败错误
   */
  static textureCreationFailed(message: string, details?: unknown): InferenceError {
    return new InferenceError(
      InferenceErrorType.TEXTURE_CREATION_FAILED,
      `Texture creation failed: ${message}`,
      details
    );
  }

  /**
   * 创建着色器编译错误
   */
  static shaderCompilationError(shaderType: string, error: string): InferenceError {
    return new InferenceError(
      InferenceErrorType.SHADER_COMPILATION_ERROR,
      `${shaderType} shader compilation failed: ${error}`,
      { shaderType, error }
    );
  }

  /**
   * 创建推理执行错误
   */
  static inferenceExecutionError(message: string, details?: unknown): InferenceError {
    return new InferenceError(
      InferenceErrorType.INFERENCE_EXECUTION_ERROR,
      `Inference execution error: ${message}`,
      details
    );
  }

  /**
   * 创建内存分配错误
   */
  static memoryAllocationError(message: string, details?: unknown): InferenceError {
    return new InferenceError(
      InferenceErrorType.MEMORY_ALLOCATION_ERROR,
      `Memory allocation error: ${message}`,
      details
    );
  }
}

// 重新导出InferenceErrorType
export { InferenceErrorType } from '../types/index.js';