import { load, Type } from 'protobufjs';
import { 
  ComputationGraph, 
  OperationNode, 
  ModelInfo, 
  InputSpec, 
  OutputSpec 
} from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';

// ONNX 数据类型映射
const ONNX_DATA_TYPES = {
  UNDEFINED: 0,
  FLOAT: 1,
  UINT8: 2,
  INT8: 3,
  UINT16: 4,
  INT16: 5,
  INT32: 6,
  INT64: 7,
  STRING: 8,
  BOOL: 9,
  FLOAT16: 10,
  DOUBLE: 11,
  UINT32: 12,
  UINT64: 13,
  COMPLEX64: 14,
  COMPLEX128: 15,
  BFLOAT16: 16
};

// 属性类型枚举
const ATTRIBUTE_TYPES = {
  UNDEFINED: 0,
  FLOAT: 1,
  INT: 2,
  STRING: 3,
  TENSOR: 4,
  GRAPH: 5,
  FLOATS: 6,
  INTS: 7,
  STRINGS: 8,
  TENSORS: 9,
  GRAPHS: 10,
  SPARSE_TENSOR: 11,
  SPARSE_TENSORS: 12,
  TYPE_PROTO: 13,
  TYPE_PROTOS: 14
};

/**
 * ONNX模型解析器
 * 负责解析ONNX模型文件，提取计算图和权重信息
 */
export class ONNXModelParser {
  private modelProto: any = null;
  private graphProto: any = null;
  private protoRoot: any = null;
  private initializers: Map<string, { data: Float32Array; shape: number[]; dataType: number }> = new Map();
  private inputSpecs: InputSpec[] = [];
  private outputSpecs: OutputSpec[] = [];
  private nodeWeights: Map<string, string[]> = new Map(); // 节点到权重名称的映射

  /**
   * 加载并解析ONNX模型
   */
  public async loadModel(modelBuffer: ArrayBuffer): Promise<void> {
    try {
      console.log('开始加载protobuf定义...');
      // 加载protobuf定义
      this.protoRoot = await load('/onnx.proto');
      console.log('protobuf定义加载成功');
      
      const ModelProto = this.protoRoot.lookupType('onnx.ModelProto');
      console.log('ModelProto类型找到:', !!ModelProto);
      
      // 解析模型数据
      console.log('开始解析模型数据, 大小:', modelBuffer.byteLength);
      const uint8Array = new Uint8Array(modelBuffer);
      this.modelProto = ModelProto.decode(uint8Array);
      console.log('模型解析成功, graph存在:', !!this.modelProto.graph);
      
      if (!this.modelProto.graph) {
        throw InferenceError.modelParseError('Model does not contain a graph');
      }
      
      this.graphProto = this.modelProto.graph;
      
      // 解析模型组件
      console.log('开始解析模型组件...');
      this.parseInitializers();
      this.parseInputSpecs();
      this.parseOutputSpecs();
      console.log('模型加载完成');
      
    } catch (error) {
      console.error('模型加载失败:', error);
      if (error instanceof InferenceError) {
        throw error;
      }
      throw InferenceError.modelParseError(
        'Failed to parse ONNX model',
        error
      );
    }
  }

  /**
   * 解析权重初始化器
   */
  private parseInitializers(): void {
    if (!this.graphProto.initializer) {
      console.log('模型中没有初始化器');
      return;
    }

    console.log(`开始解析 ${this.graphProto.initializer.length} 个初始化器`);
    for (const tensor of this.graphProto.initializer) {
      const name = tensor.name;
      const shape = tensor.dims || [];
      const dataType = tensor.dataType || ONNX_DATA_TYPES.FLOAT;
      const data = this.parseTensorData(tensor);
      
      this.initializers.set(name, {
        data,
        shape,
        dataType
      });
      
      console.log(`初始化器 ${name}: 形状 [${shape.join(', ')}], 数据量 ${data.length}`);
    }
  }

  /**
   * 解析张量数据
   */
  private parseTensorData(tensorProto: any): Float32Array {
    const dims = tensorProto.dims || [];
    const totalSize = dims.reduce((acc: number, dim: number) => acc * dim, 1);
    
    let data: Float32Array;

    // 根据数据类型解析
    if (tensorProto.rawData && tensorProto.rawData.length > 0) {
      // 从原始字节数据解析
      data = this.parseRawData(tensorProto.rawData, tensorProto.dataType, totalSize);
    } else if (tensorProto.floatData && tensorProto.floatData.length > 0) {
      // 从浮点数据解析
      data = new Float32Array(tensorProto.floatData);
    } else if (tensorProto.int32Data && tensorProto.int32Data.length > 0) {
      // 从整数数据解析
      data = new Float32Array(tensorProto.int32Data);
    } else {
      // 创建零填充数组
      data = new Float32Array(totalSize);
    }

    return data;
  }

  /**
   * 解析原始字节数据
   */
  private parseRawData(rawData: Uint8Array, dataType: number, totalSize: number): Float32Array {
    const buffer = rawData.buffer.slice(rawData.byteOffset, rawData.byteOffset + rawData.byteLength);
    
    switch (dataType) {
      case ONNX_DATA_TYPES.FLOAT:
        return new Float32Array(buffer);
        
      case ONNX_DATA_TYPES.DOUBLE:
        const float64Array = new Float64Array(buffer);
        return new Float32Array(float64Array);
        
      case ONNX_DATA_TYPES.INT32:
        const int32Array = new Int32Array(buffer);
        return new Float32Array(int32Array);
        
      case ONNX_DATA_TYPES.INT64:
        const int64Array = new BigInt64Array(buffer);
        const numberArray = Array.from(int64Array, x => Number(x));
        return new Float32Array(numberArray);
        
      case ONNX_DATA_TYPES.UINT8:
        const uint8Array = new Uint8Array(buffer);
        return new Float32Array(uint8Array);
        
      default:
        console.warn(`Unsupported data type: ${dataType}, creating zero array`);
        return new Float32Array(totalSize);
    }
  }

  /**
   * 解析输入规格
   */
  private parseInputSpecs(): void {
    if (!this.graphProto.input) {
      return;
    }

    for (const input of this.graphProto.input) {
      // 跳过权重张量
      if (this.initializers.has(input.name)) {
        continue;
      }

      const spec: InputSpec = {
        name: input.name,
        shape: this.parseShape(input.type?.tensorType?.shape),
        type: this.getDataTypeName(input.type?.tensorType?.elemType || ONNX_DATA_TYPES.FLOAT)
      };
      
      this.inputSpecs.push(spec);
    }
  }

  /**
   * 解析输出规格
   */
  private parseOutputSpecs(): void {
    if (!this.graphProto.output) {
      return;
    }

    for (const output of this.graphProto.output) {
      const spec: OutputSpec = {
        name: output.name,
        shape: this.parseShape(output.type?.tensorType?.shape),
        type: this.getDataTypeName(output.type?.tensorType?.elemType || ONNX_DATA_TYPES.FLOAT)
      };
      
      this.outputSpecs.push(spec);
    }
  }

  /**
   * 解析张量形状
   */
  private parseShape(shapeProto: any): number[] {
    if (!shapeProto || !shapeProto.dim) {
      return [];
    }

    return shapeProto.dim.map((dim: any) => {
      if (dim.dimValue !== undefined) {
        return Number(dim.dimValue);
      } else if (dim.dimParam !== undefined) {
        // 动态维度，返回-1表示
        return -1;
      }
      return 1; // 默认值
    });
  }

  /**
   * 获取数据类型名称
   */
  private getDataTypeName(dataType: number): string {
    const typeNames = Object.keys(ONNX_DATA_TYPES);
    const typeValues = Object.values(ONNX_DATA_TYPES);
    const index = typeValues.indexOf(dataType);
    return index >= 0 ? (typeNames[index] || 'UNKNOWN') : 'UNKNOWN';
  }

  /**
   * 解析计算图
   */
  public parseGraph(): ComputationGraph {
    if (!this.graphProto) {
      throw InferenceError.modelParseError('Model not loaded');
    }

    const nodes: OperationNode[] = [];
    
    console.log(`开始解析 ${this.graphProto.node?.length || 0} 个节点`);
    // 解析节点
    for (const nodeProto of this.graphProto.node || []) {
      const nodeInputs = nodeProto.input || [];
      const nodeWeightNames = this.getNodeWeights(nodeInputs);
      
      const node: OperationNode = {
        id: nodeProto.name || `node_${nodes.length}`,
        type: nodeProto.opType,
        inputs: nodeInputs,
        outputs: nodeProto.output || [],
        attributes: this.parseAttributes(nodeProto.attribute || []),
        weights: nodeWeightNames
      };
      
      // 存储节点权重关系
      this.nodeWeights.set(node.id, nodeWeightNames);
      
      console.log(`节点 ${node.id} (操作: ${node.type}): 输入 ${nodeInputs.length} 个, 权重 ${nodeWeightNames.length} 个`);
      nodes.push(node);
    }

    // 构建计算图
    const graph: ComputationGraph = {
      nodes,
      inputs: this.inputSpecs.map(spec => spec.name),
      outputs: this.outputSpecs.map(spec => spec.name),
      initializers: new Map(Array.from(this.initializers.entries()).map(([key, value]) => [key, value.data]))
    };

    return graph;
  }
  
  /**
   * 获取节点的权重名称
   */
  private getNodeWeights(inputs: string[]): string[] {
    const weights: string[] = [];
    
    for (const input of inputs) {
      if (this.initializers.has(input)) {
        weights.push(input);
      }
    }
    
    return weights;
  }

  /**
   * 解析节点属性
   */
  private parseAttributes(attributes: any[]): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const attr of attributes) {
      const name = attr.name;
      const type = attr.type;

      switch (type) {
        case ATTRIBUTE_TYPES.FLOAT:
          result[name] = attr.f;
          break;
          
        case ATTRIBUTE_TYPES.INT:
          result[name] = Number(attr.i);
          break;
          
        case ATTRIBUTE_TYPES.STRING:
          result[name] = attr.s;
          break;
          
        case ATTRIBUTE_TYPES.FLOATS:
          result[name] = attr.floats || [];
          break;
          
        case ATTRIBUTE_TYPES.INTS:
          result[name] = (attr.ints || []).map((x: any) => Number(x));
          break;
          
        case ATTRIBUTE_TYPES.STRINGS:
          result[name] = attr.strings || [];
          break;
          
        case ATTRIBUTE_TYPES.TENSOR:
          if (attr.t) {
            result[name] = this.parseTensorData(attr.t);
          }
          break;
          
        default:
          console.warn(`Unsupported attribute type: ${type} for attribute: ${name}`);
          result[name] = null;
      }
    }

    return result;
  }

  /**
   * 提取权重数据
   */
  public extractWeights(): Map<string, Float32Array> {
    const weightMap = new Map<string, Float32Array>();
    for (const [name, weightInfo] of this.initializers.entries()) {
      weightMap.set(name, weightInfo.data);
    }
    return weightMap;
  }
  
  /**
   * 获取权重信息（包括形状和数据类型）
   */
  public getWeightInfo(name: string): { data: Float32Array; shape: number[]; dataType: number } | null {
    return this.initializers.get(name) || null;
  }
  
  /**
   * 获取节点的权重名称列表
   */
  public getNodeWeightNames(nodeId: string): string[] {
    return this.nodeWeights.get(nodeId) || [];
  }

  /**
   * 获取输入规格
   */
  public getInputSpecs(): InputSpec[] {
    return [...this.inputSpecs];
  }

  /**
   * 获取输出规格
   */
  public getOutputSpecs(): OutputSpec[] {
    return [...this.outputSpecs];
  }

  /**
   * 获取模型信息
   */
  public getModelInfo(): ModelInfo {
    if (!this.graphProto) {
      throw InferenceError.modelParseError('Model not loaded');
    }

    const inputShape = this.inputSpecs.length > 0 ? (this.inputSpecs[0]?.shape || []) : [];
    const outputShape = this.outputSpecs.length > 0 ? (this.outputSpecs[0]?.shape || []) : [];
    const operatorCount = this.graphProto.node?.length || 0;
    
    // 计算模型大小
    let modelSize = 0;
    for (const weightInfo of this.initializers.values()) {
      modelSize += weightInfo.data.byteLength;
    }

    return {
      inputShape,
      outputShape,
      operatorCount,
      modelSize
    };
  }

  /**
   * 验证模型
   */
  public validateModel(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.modelProto) {
      errors.push('Model not loaded');
      return { isValid: false, errors };
    }

    if (!this.graphProto) {
      errors.push('Model does not contain a graph');
      return { isValid: false, errors };
    }

    if (!this.graphProto.node || this.graphProto.node.length === 0) {
      errors.push('Graph does not contain any nodes');
    }

    if (this.inputSpecs.length === 0) {
      errors.push('Model does not have any inputs');
    }

    if (this.outputSpecs.length === 0) {
      errors.push('Model does not have any outputs');
    }

    // 检查节点完整性
    for (const node of this.graphProto.node || []) {
      if (!node.opType) {
        errors.push(`Node ${node.name || 'unnamed'} has no operation type`);
      }
      
      if (!node.output || node.output.length === 0) {
        errors.push(`Node ${node.name || 'unnamed'} has no outputs`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.modelProto = null;
    this.graphProto = null;
    this.protoRoot = null;
    this.initializers.clear();
    this.nodeWeights.clear();
    this.inputSpecs.length = 0;
    this.outputSpecs.length = 0;
  }
}