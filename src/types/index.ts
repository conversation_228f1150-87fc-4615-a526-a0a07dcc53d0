// 错误类型枚举
export enum InferenceErrorType {
  WEBGL_NOT_SUPPORTED = 'WEBGL_NOT_SUPPORTED',
  EXTENSION_NOT_AVAILABLE = 'EXTENSION_NOT_AVAILABLE',
  MODEL_PARSE_ERROR = 'MODEL_PARSE_ERROR',
  UNSUPPORTED_OPERATOR = 'UNSUPPORTED_OPERATOR',
  TEXTURE_CREATION_FAILED = 'TEXTURE_CREATION_FAILED',
  SHADER_COMPILATION_ERROR = 'SHADER_COMPILATION_ERROR',
  INFERENCE_EXECUTION_ERROR = 'INFERENCE_EXECUTION_ERROR',
  MEMORY_ALLOCATION_ERROR = 'MEMORY_ALLOCATION_ERROR'
}

// WebGL ONNX推理配置
export interface WebGLONNXInferenceConfig {
  canvas: HTMLCanvasElement;
  precision?: 'highp' | 'mediump' | 'lowp';
  enableOptimizations?: boolean;
  maxTextureSize?: number;
  batchSize?: number;
  debugMode?: boolean; // 启用 debug 模式，在 debug 模式下才执行 WebGL 错误检查以提升性能
}

// 推理结果
export interface InferenceResult {
  outputTexture: WebGLTexture;
  inferenceTime: number;
  memoryUsage: number;
}

// 模型信息
export interface ModelInfo {
  inputShape: number[];
  outputShape: number[];
  operatorCount: number;
  modelSize: number;
}

// 性能指标
export interface PerformanceMetrics {
  averageInferenceTime: number;
  peakMemoryUsage: number;
  texturePoolSize: number;
  totalInferences: number;
  fps: number;
}

// 纹理规格
export interface TextureSpec {
  width: number;
  height: number;
  format: number;
  type: number;
  internalFormat: number;
  size: number;
}

// 计算图节点
export interface OperationNode {
  id: string;
  type: string;
  inputs: string[];
  outputs: string[];
  attributes: Record<string, unknown>;
  weights?: string[]; // 节点关联的权重名称
}

// 计算图
export interface ComputationGraph {
  nodes: OperationNode[];
  inputs: string[];
  outputs: string[];
  initializers: Map<string, Float32Array>;
}

// 输入规格
export interface InputSpec {
  name: string;
  shape: number[];
  type: string;
}

// 输出规格
export interface OutputSpec {
  name: string;
  shape: number[];
  type: string;
}

// 着色器操作
export interface ShaderOperation {
  vertexShader: string;
  fragmentShader: string;
  uniforms: Record<string, unknown>;
  inputTextures: string[];
  outputTexture: string;
}

// 操作符实现接口
export interface OperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation;
  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void;
  getOutputShape(inputShapes: number[][]): number[];
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// WebGL扩展需求
export interface WebGLExtensions {
  EXT_color_buffer_float?: EXT_color_buffer_float;
  OES_texture_float_linear?: OES_texture_float_linear;
  EXT_float_blend?: EXT_float_blend;
  WEBGL_color_buffer_float?: WEBGL_color_buffer_float;
}

// WebGL上下文管理器接口
export interface IWebGLContextManager {
  initialize(canvas: HTMLCanvasElement): boolean;
  isReady(): boolean;
  getContext(): WebGL2RenderingContext;
  createTexture(
    width: number,
    height: number,
    format: number,
    internalFormat: number,
    type: number,
    data?: ArrayBufferView | null
  ): WebGLTexture;
  createFramebuffer(): WebGLFramebuffer;
  bindFramebufferTexture(framebuffer: WebGLFramebuffer, texture: WebGLTexture): void;
  checkError(operation?: string): void;
  getMaxTextureSize(): number;
  isDebugMode(): boolean;
  dispose(): void;
}

// 主接口
export interface IWebGLONNXInference {
  // 初始化推理引擎
  initialize(config: WebGLONNXInferenceConfig): Promise<boolean>;

  // 加载ONNX模型
  loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo>;

  // 设置输入纹理
  setInputTexture(texture: WebGLTexture): void;
  setInputFromImage(image: HTMLImageElement): void;
  setInputFromCanvas(canvas: HTMLCanvasElement): void;
  setInputFromImageData(imageData: ImageData): void;

  // 执行推理
  inference(): Promise<InferenceResult>;

  // 获取输出
  getOutputTexture(): WebGLTexture | null;
  getOutputAsImageData(): ImageData | null;
  getOutputAsFloat32Array(): Float32Array | null;
  renderToCanvas(canvas: HTMLCanvasElement): void;
  checkRenderResult(): { isAllBlack: boolean; isAllWhite: boolean; };
  renderToCanvasWithCheck(canvas: HTMLCanvasElement): { isAllBlack: boolean; isAllWhite: boolean; };
  getIntermediateResults(): Promise<Array<{ imageData: ImageData, label: string; }>>;

  // 性能监控
  getPerformanceMetrics(): PerformanceMetrics;

  // 资源管理
  dispose(): void;
}
