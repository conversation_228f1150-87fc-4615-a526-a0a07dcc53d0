/**
 * WebGL ONNX Inference Library
 * 纯WebGL实现的ONNX模型推理库
 * 
 * <AUTHOR> ONNX Inference Team
 * @version 1.0.0
 */

// 主要API
export { WebGLONNXInference } from './core/WebGLONNXInference.js';

// 错误处理
export { InferenceError, InferenceErrorType } from './errors/InferenceError.js';

// 类型定义
export type {
  WebGLONNXInferenceConfig,
  ModelInfo,
  InferenceResult,
  PerformanceMetrics,
  TextureSpec,
  ComputationGraph,
  OperationNode,
  InputSpec,
  OutputSpec,
  ShaderOperation,
  OperatorImplementation,
  ValidationResult,
  WebGLExtensions,
  IWebGLONNXInference
} from './types/index.js';

// 核心组件（高级用法）
export { WebGLContextManager } from './webgl/WebGLContextManager.js';
export { TextureManager } from './webgl/TextureManager.js';
export { ShaderGenerator } from './shaders/ShaderGenerator.js';
export { GPUInferenceEngine } from './core/GPUInferenceEngine.js';
export { ONNXModelParser } from './onnx/ONNXModelParser.js';
export { OperatorMapper } from './operators/OperatorMapper.js';

// 版本信息
export const VERSION = '1.0.0';

/**
 * 检查WebGL支持
 */
export function checkWebGLSupport(): boolean {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    return gl !== null;
  } catch {
    return false;
  }
}

/**
 * 获取WebGL能力信息
 */
export function getWebGLCapabilities(): {
  maxTextureSize: number;
  maxVertexTextures: number;
  maxFragmentTextures: number;
  maxVaryingVectors: number;
  extensions: string[];
} | null {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');
    
    if (!gl) return null;
    
    return {
      maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
      maxVertexTextures: gl.getParameter(gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),
      maxFragmentTextures: gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS),
      maxVaryingVectors: gl.getParameter(gl.MAX_VARYING_VECTORS),
      extensions: gl.getSupportedExtensions() || []
    };
  } catch {
    return null;
  }
}