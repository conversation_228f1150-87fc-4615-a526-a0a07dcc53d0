import { ShaderOperation } from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';
import { WebGLContextManager } from '../webgl/WebGLContextManager.js';
import {
  VERTEX_SHADER_TEMPLATE,
  FRAGMENT_SHADER_BASE,
  CONV2D_FRAGMENT_SHADER,
  RELU_FRAGMENT_SHADER,
  MAXPOOL2D_FRAGMENT_SHADER,
  BATCH_NORM_FRAGMENT_SHADER,
  ADD_FRAGMENT_SHADER,
  MUL_FRAGMENT_SHADER,
  SUB_FRAGMENT_SHADER,
  SOFTMAX_FRAGMENT_SHADER,
  RESHAPE_FRAGMENT_SHADER,
  GRAYSCALE_FRAGMENT_SHADER,
  COPY_FRAGMENT_SHADER,
  CONV1X1_GRAYSCALE_FRAGMENT_SHADER,
  CONSTANT_FRAGMENT_SHADER,
  TRANSPOSE_FRAGMENT_SHADER,
  PRELU_FRAGMENT_SHADER
} from './ShaderTemplates.js';

/**
 * 着色器生成器
 * 负责动态生成和编译WebGL着色器
 */
export class ShaderGenerator {
  private contextManager: WebGLContextManager;
  private compiledPrograms: Map<string, WebGLProgram> = new Map();
  private shaderCache: Map<string, string> = new Map();

  constructor(contextManager: WebGLContextManager) {
    this.contextManager = contextManager;
  }

  /**
   * 生成顶点着色器
   */
  public generateVertexShader(): string {
    return VERTEX_SHADER_TEMPLATE;
  }

  /**
   * 根据操作类型生成片段着色器
   */
  public generateFragmentShader(operation: string, params?: Record<string, unknown>): string {
    const cacheKey = `${operation}_${JSON.stringify(params || {})}`;

    if (this.shaderCache.has(cacheKey)) {
      return this.shaderCache.get(cacheKey)!;
    }

    let shaderSource: string;

    switch (operation.toLowerCase()) {
      case 'conv':
      case 'conv2d':
        shaderSource = this.generateConv2DShader(params);
        break;

      case 'conv1x1_grayscale':
        shaderSource = CONV1X1_GRAYSCALE_FRAGMENT_SHADER;
        break;

      case 'constant':
        shaderSource = CONSTANT_FRAGMENT_SHADER;
        break;

      case 'relu':
        shaderSource = RELU_FRAGMENT_SHADER;
        break;

      case 'prelu':
        shaderSource = PRELU_FRAGMENT_SHADER;
        break;

      case 'maxpool':
      case 'maxpool2d':
        shaderSource = MAXPOOL2D_FRAGMENT_SHADER;
        break;

      case 'batchnormalization':
      case 'batchnorm':
        shaderSource = BATCH_NORM_FRAGMENT_SHADER;
        break;

      case 'add':
        shaderSource = ADD_FRAGMENT_SHADER;
        break;

      case 'sub':
        shaderSource = SUB_FRAGMENT_SHADER;
        break;

      case 'mul':
        shaderSource = MUL_FRAGMENT_SHADER;
        break;

      case 'softmax':
        shaderSource = SOFTMAX_FRAGMENT_SHADER;
        break;

      case 'reshape':
        shaderSource = RESHAPE_FRAGMENT_SHADER;
        break;

      case 'transpose':
        shaderSource = TRANSPOSE_FRAGMENT_SHADER;
        break;

      case 'grayscale':
        shaderSource = GRAYSCALE_FRAGMENT_SHADER;
        break;

      case 'copy':
        shaderSource = COPY_FRAGMENT_SHADER;
        break;

      case 'concat':
      case 'resize':
      case 'slice':
      case 'globalaverage':
      case 'globalaveragepool':
        // 对于复杂操作，使用复制作为简化实现
        shaderSource = COPY_FRAGMENT_SHADER;
        break;

      default:
        throw InferenceError.unsupportedOperator(operation);
    }

    this.shaderCache.set(cacheKey, shaderSource);
    return shaderSource;
  }

  /**
   * 生成卷积操作的特定着色器
   */
  private generateConv2DShader(params?: Record<string, unknown>): string {
    // 根据参数自定义卷积着色器
    let shader = CONV2D_FRAGMENT_SHADER;

    if (params) {
      // 可以根据卷积参数进行着色器优化
      const kernelSize = params.kernelShape as number[] || [3, 3];
      const stride = params.strides as number[] || [1, 1];
      const padding = params.pads as number[] || [0, 0, 0, 0];

      // 对于小的固定核大小，可以展开循环优化性能
      if (kernelSize[0] === 3 && kernelSize[1] === 3) {
        shader = this.generateOptimizedConv3x3Shader();
      }
    }

    return shader;
  }

  /**
   * 生成优化的3x3卷积着色器（展开循环）
   */
  private generateOptimizedConv3x3Shader(): string {
    return `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_kernel;
uniform sampler2D u_bias;
uniform vec2 u_inputSize;
uniform vec2 u_stride;
uniform vec2 u_padding;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec2 outputPos = v_texCoord * u_inputSize;
    vec4 result = texture(u_bias, vec2(0.0, 0.0));
    
    // 展开的3x3卷积循环
    vec2 basePos = outputPos * u_stride - u_padding;
    
    // 手动展开9个卷积计算
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            vec2 inputPos = basePos + vec2(float(i), float(j));
            vec2 inputCoord = inputPos / u_inputSize;
            
            if(inputCoord.x >= 0.0 && inputCoord.x <= 1.0 && 
               inputCoord.y >= 0.0 && inputCoord.y <= 1.0) {
                vec4 inputValue = texture(u_input, inputCoord);
                vec2 kernelCoord = vec2(float(i), float(j)) / 3.0;
                vec4 kernelValue = texture(u_kernel, kernelCoord);
                result += inputValue * kernelValue;
            }
        }
    }
    
    fragColor = result;
}
`;
  }

  /**
   * 编译着色器
   */
  public compileShader(
    gl: WebGL2RenderingContext,
    source: string,
    type: number
  ): WebGLShader {
    const shader = gl.createShader(type);
    if (!shader) {
      throw InferenceError.shaderCompilationError(
        type === gl.VERTEX_SHADER ? 'vertex' : 'fragment',
        'Failed to create shader object'
      );
    }

    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      const error = gl.getShaderInfoLog(shader) || 'Unknown compilation error';
      gl.deleteShader(shader);
      throw InferenceError.shaderCompilationError(
        type === gl.VERTEX_SHADER ? 'vertex' : 'fragment',
        error
      );
    }

    return shader;
  }

  /**
   * 创建着色器程序
   */
  public createProgram(
    gl: WebGL2RenderingContext,
    vertexShader: WebGLShader,
    fragmentShader: WebGLShader
  ): WebGLProgram {
    const program = gl.createProgram();
    if (!program) {
      throw InferenceError.shaderCompilationError('program', 'Failed to create program object');
    }

    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      const error = gl.getProgramInfoLog(program) || 'Unknown linking error';
      gl.deleteProgram(program);
      throw InferenceError.shaderCompilationError('program', error);
    }

    return program;
  }

  /**
   * 编译完整的着色器程序
   */
  public compileShaderProgram(operation: string, params?: Record<string, unknown>): WebGLProgram {
    const programKey = `${operation}_${JSON.stringify(params || {})}`;

    if (this.compiledPrograms.has(programKey)) {
      return this.compiledPrograms.get(programKey)!;
    }

    const gl = this.contextManager.getContext();

    // 生成着色器源码
    const vertexSource = this.generateVertexShader();
    const fragmentSource = this.generateFragmentShader(operation, params);

    // 编译着色器
    const vertexShader = this.compileShader(gl, vertexSource, gl.VERTEX_SHADER);
    const fragmentShader = this.compileShader(gl, fragmentSource, gl.FRAGMENT_SHADER);

    // 创建程序
    const program = this.createProgram(gl, vertexShader, fragmentShader);

    // 清理中间着色器对象
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);

    // 缓存程序
    this.compiledPrograms.set(programKey, program);

    return program;
  }

  /**
   * 创建着色器操作描述
   */
  public createShaderOperation(
    operation: string,
    params: Record<string, unknown>,
    inputTextures: string[],
    outputTexture: string
  ): ShaderOperation {
    const vertexShader = this.generateVertexShader();
    const fragmentShader = this.generateFragmentShader(operation, params);

    return {
      vertexShader,
      fragmentShader,
      uniforms: params,
      inputTextures,
      outputTexture
    };
  }

  /**
   * 设置着色器程序的uniform变量
   */
  public setupUniforms(
    gl: WebGL2RenderingContext,
    program: WebGLProgram,
    uniforms: Record<string, unknown>
  ): void {
    gl.useProgram(program);

    for (const [name, value] of Object.entries(uniforms)) {
      const location = gl.getUniformLocation(program, name);
      if (location === null) {
        console.warn(`Uniform ${name} not found in shader`);
        continue;
      }

      this.setUniformValue(gl, location, value);
    }
  }

  /**
   * 设置uniform值
   */
  private setUniformValue(
    gl: WebGL2RenderingContext,
    location: WebGLUniformLocation,
    value: unknown
  ): void {
    if (typeof value === 'number') {
      gl.uniform1f(location, value);
    } else if (typeof value === 'boolean') {
      gl.uniform1i(location, value ? 1 : 0);
    } else if (Array.isArray(value)) {
      switch (value.length) {
        case 2:
          gl.uniform2fv(location, value);
          break;
        case 3:
          gl.uniform3fv(location, value);
          break;
        case 4:
          gl.uniform4fv(location, value);
          break;
        default:
          console.warn(`Unsupported uniform array length: ${value.length}`);
      }
    } else {
      console.warn(`Unsupported uniform type: ${typeof value}`);
    }
  }

  /**
   * 优化着色器源码
   */
  public optimizeShader(source: string): string {
    // 简单的着色器优化
    return source
      .replace(/\s+/g, ' ') // 压缩空白字符
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\/\/.*$/gm, '') // 移除单行注释
      .trim();
  }

  /**
   * 获取着色器编译统计
   */
  public getCompilationStats(): { programCount: number; cacheHits: number; } {
    return {
      programCount: this.compiledPrograms.size,
      cacheHits: this.shaderCache.size
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    const gl = this.contextManager.getContext();

    // 删除所有编译的程序
    for (const program of this.compiledPrograms.values()) {
      gl.deleteProgram(program);
    }

    this.compiledPrograms.clear();
    this.shaderCache.clear();
  }
}