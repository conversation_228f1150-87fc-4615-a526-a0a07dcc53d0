/**
 * 顶点着色器模板
 * 用于创建全屏四边形以进行纹理处理
 */
export const VERTEX_SHADER_TEMPLATE = `#version 300 es
precision highp float;

// 顶点属性
in vec2 a_position;
in vec2 a_texCoord;

// 输出到片段着色器
out vec2 v_texCoord;

void main() {
    gl_Position = vec4(a_position, 0.0, 1.0);
    v_texCoord = a_texCoord;
}
`;

/**
 * 片段着色器基础模板
 */
export const FRAGMENT_SHADER_BASE = `#version 300 es
precision highp float;

// 输入纹理坐标
in vec2 v_texCoord;

// 输出颜色
out vec4 fragColor;

`;

/**
 * 卷积操作着色器模板
 */
export const CONV2D_FRAGMENT_SHADER = `#version 300 es
precision highp float;

// 输入
uniform sampler2D u_input;
uniform sampler2D u_kernel;

// 参数
uniform vec2 u_inputSize;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputValue = texture(u_input, v_texCoord);
    vec4 kernelWeights = texture(u_kernel, vec2(0.0, 0.0));
    
    // 简单的灰度化卷积：直接与RGB权重相乘
    float result = dot(inputValue.rgb, kernelWeights.rgb);
    
    fragColor = vec4(result, result, result, 1.0);
}
`;

/**
 * ReLU激活函数着色器模板
 */
export const RELU_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputValue = texture(u_input, v_texCoord);
    fragColor = max(inputValue, 0.0);
}
`;

/**
 * 最大池化着色器模板
 */
export const MAXPOOL2D_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform vec2 u_inputSize;
uniform vec2 u_poolSize;
uniform vec2 u_stride;
uniform vec2 u_padding;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec2 startPos = v_texCoord * u_inputSize * u_stride - u_padding;
    vec4 maxValue = vec4(-1000.0);
    
    for(float i = 0.0; i < u_poolSize.x; i++) {
        for(float j = 0.0; j < u_poolSize.y; j++) {
            vec2 samplePos = (startPos + vec2(i, j)) / u_inputSize;
            if(samplePos.x >= 0.0 && samplePos.x <= 1.0 && 
               samplePos.y >= 0.0 && samplePos.y <= 1.0) {
                vec4 value = texture(u_input, samplePos);
                maxValue = max(maxValue, value);
            }
        }
    }
    
    fragColor = maxValue;
}
`;

/**
 * 批归一化着色器模板
 */
export const BATCH_NORM_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_scale;
uniform sampler2D u_bias;
uniform sampler2D u_mean;
uniform sampler2D u_var;
uniform float u_epsilon;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 input = texture(u_input, v_texCoord);
    vec4 mean = texture(u_mean, vec2(0.0, 0.0));
    vec4 variance = texture(u_var, vec2(0.0, 0.0));
    vec4 scale = texture(u_scale, vec2(0.0, 0.0));
    vec4 bias = texture(u_bias, vec2(0.0, 0.0));
    
    // 批归一化公式: (x - mean) / sqrt(var + epsilon) * scale + bias
    vec4 normalized = (input - mean) / sqrt(variance + u_epsilon);
    fragColor = normalized * scale + bias;
}
`;

/**
 * 逐元素加法着色器模板
 */
export const ADD_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_bias;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputColor = texture(u_input, v_texCoord);
    vec4 bias = texture(u_bias, vec2(0.0, 0.0));
    fragColor = inputColor + bias;
}
`;

/**
 * 逐元素乘法着色器模板
 */
export const MUL_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_inputA;
uniform sampler2D u_inputB;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 a = texture(u_inputA, v_texCoord);
    vec4 b = texture(u_inputB, v_texCoord);
    fragColor = a * b;
}
`;

/**
 * 逐元素减法着色器模板
 */
export const SUB_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_bias;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputColor = texture(u_input, v_texCoord);
    vec4 bias = texture(u_bias, vec2(0.0, 0.0));
    fragColor = inputColor - bias;
}
`;

/**
 * Softmax着色器模板
 */
export const SOFTMAX_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform vec2 u_inputSize;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputColor = texture(u_input, v_texCoord);
    
    // 简化的Softmax实现（针对单个像素）
    // 实际实现需要考虑整个张量的最大值和和
    vec4 expValues = exp(inputColor);
    float sum = expValues.x + expValues.y + expValues.z + expValues.w;
    
    fragColor = expValues / sum;
}
`;

/**
 * 形状重塑着色器模板
 */
export const RESHAPE_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform vec2 u_inputSize;
uniform vec2 u_outputSize;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    // 计算在输出纹理中的线性索引
    vec2 outputPos = v_texCoord * u_outputSize;
    float outputIndex = outputPos.y * u_outputSize.x + outputPos.x;
    
    // 转换为输入纹理坐标
    float inputIndex = outputIndex;
    float inputY = floor(inputIndex / u_inputSize.x);
    float inputX = inputIndex - inputY * u_inputSize.x;
    vec2 inputCoord = vec2(inputX, inputY) / u_inputSize;
    
    fragColor = texture(u_input, inputCoord);
}
`;

/**
 * 灰度转换着色器模板
 */
export const GRAYSCALE_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 color = texture(u_input, v_texCoord);
    
    // 使用标准灰度转换公式
    float gray = dot(color.rgb, vec3(0.299, 0.587, 0.114));
    
    fragColor = vec4(gray, gray, gray, 1.0);
}
`;

/**
 * 纹理复制着色器模板
 */
export const COPY_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    fragColor = texture(u_input, v_texCoord);
}
`;

/**
 * 1x1卷积灰度化着色器模板
 * 专门为RGB到灰度转换设计
 */
export const CONV1X1_GRAYSCALE_FRAGMENT_SHADER = `#version 300 es
precision highp float;

// 输入
 uniform sampler2D u_input;
uniform sampler2D u_weights;

// 参数
uniform vec2 u_inputSize;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    // 获取RGB输入
    vec4 inputColor = texture(u_input, v_texCoord);
    
    // 获取灰度化权重 (0.299, 0.587, 0.114)
    vec4 weights = texture(u_weights, vec2(0.5, 0.5));
    
    // 计算灰度值： Gray = 0.299*R + 0.587*G + 0.114*B
    float gray = dot(inputColor.rgb, weights.rgb);
    
    // 输出灰度图像
    fragColor = vec4(gray, gray, gray, 1.0);
}
`;

/**
 * 常量操作着色器模板
 * 用于生成常量纹理
 */
export const CONSTANT_FRAGMENT_SHADER = `#version 300 es
precision highp float;

// 常量参数
uniform vec3 u_constantValue;
uniform bool u_hasFloatData;

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    if (u_hasFloatData) {
        // 使用提供的常量值
        fragColor = vec4(u_constantValue, 1.0);
    } else {
        // 默认灰度化权重
        fragColor = vec4(0.299, 0.587, 0.114, 1.0);
    }
}
`;

/**
 * 转置操作着色器模板
 * 用于对纹理进行轴交换
 */
export const TRANSPOSE_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform vec2 u_inputSize;
uniform ivec4 u_perm; // 转置排列，默认[0,1,2,3]变为[0,2,1,3]

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    // 对于简单的转置操作，直接复制输入
    // 实际应用中需要根据转置维度进行坐标变换
    vec2 transposedCoord = v_texCoord;
    
    // 简化实现：对于常见的(H,W)转置，交换x和y坐标
    if (u_perm.x == 0 && u_perm.y == 2 && u_perm.z == 1 && u_perm.w == 3) {
        // NCHW -> NHWC 转换
        transposedCoord = vec2(v_texCoord.y, v_texCoord.x);
    }
    
    fragColor = texture(u_input, transposedCoord);
}
`;

/**
 * PRelu参数化ReLU着色器模板
 * 简化实现，直接使用ReLU激活函数
 */
export const PRELU_FRAGMENT_SHADER = `#version 300 es
precision highp float;

uniform sampler2D u_input;
uniform sampler2D u_slope; // PRelu的斜率参数

in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 inputColor = texture(u_input, v_texCoord);
    vec4 slope = texture(u_slope, vec2(0.0, 0.0));
    
    // PRelu: max(0, x) + slope * min(0, x)
    // 简化实现：在负值区域使用小的斜率
    vec4 positive = max(inputColor, 0.0);
    vec4 negative = min(inputColor, 0.0) * slope;
    
    fragColor = positive + negative;
}
`;