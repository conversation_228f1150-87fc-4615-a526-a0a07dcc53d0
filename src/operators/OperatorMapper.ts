import { OperatorImplementation, ShaderOperation, ValidationResult } from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';
import { ShaderGenerator } from '../shaders/ShaderGenerator.js';

/**
 * 基础操作符实现
 */
abstract class BaseOperatorImplementation implements OperatorImplementation {
  abstract generateShader(params: Record<string, unknown>): ShaderOperation;
  abstract setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void;
  abstract getOutputShape(inputShapes: number[][]): number[];
}

/**
 * ReLU操作符实现
 */
class ReluOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'relu',
      uniforms: params,
      inputTextures: ['u_input'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // ReLU 不需要额外的 uniform 参数
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || []; // 输出形状与输入相同
  }
}

/**
 * 卷积操作符实现
 * 支持1x1卷积和灰度化处理
 */
class Conv2DOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    const kernelShape = params.kernel_shape as number[] || [1, 1];
    const strides = params.strides as number[] || [1, 1];
    const pads = params.pads as number[] || [0, 0, 0, 0];
    
    // 针对1x1卷积优化
    if (kernelShape[0] === 1 && kernelShape[1] === 1) {
      return {
        vertexShader: '',
        fragmentShader: 'conv1x1_grayscale',
        uniforms: { ...params, isGrayscale: true },
        inputTextures: ['u_input', 'u_weights'],
        outputTexture: 'output'
      };
    }
    
    return {
      vertexShader: '',
      fragmentShader: 'conv2d',
      uniforms: params,
      inputTextures: ['u_input', 'u_weights'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    const kernelShape = params.kernel_shape as number[] || [1, 1];
    const strides = params.strides as number[] || [1, 1];
    const pads = params.pads as number[] || [0, 0, 0, 0];

    // 只设置在着色器中实际存在的uniform变量
    const inputSizeLocation = gl.getUniformLocation(program, 'u_inputSize');
    if (inputSizeLocation) {
      // 默认输入尺寸，实际使用时会被GPU引擎覆盖
      gl.uniform2f(inputSizeLocation, 224.0, 224.0);
    }
    
    // 为1x1卷积特别设置的uniform
    const isGrayscale = params.isGrayscale as boolean;
    if (isGrayscale) {
      // 1x1卷积灰度化可能需要额外的uniform设置
    }
  }

  getOutputShape(inputShapes: number[][]): number[] {
    if (!inputShapes[0] || inputShapes[0].length < 4) {
      return [1, 1, 224, 224]; // 默认输出形状
    }
    
    const [N, C, H, W] = inputShapes[0];
    // 对于1x1卷积灰度化，输出通道数为1
    return [N || 1, 1, H || 224, W || 224];
  }
}

/**
 * 加法操作符实现
 */
class AddOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'add',
      uniforms: params,
      inputTextures: ['u_input', 'u_bias'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // Add操作不需要额外的uniform参数
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || [];
  }
}

/**
 * 乘法操作符实现
 */
class MulOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'mul',
      uniforms: params,
      inputTextures: ['u_inputA', 'u_inputB'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // Mul操作不需要额外的uniform参数
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || [];
  }
}

/**
 * 减法操作符实现
 */
class SubOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'sub',
      uniforms: params,
      inputTextures: ['u_input', 'u_bias'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // Sub操作的实现
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || [];
  }
}

/**
 * 常量操作符实现
 * 支持ONNX常量值解析和纹理创建
 */
class ConstantOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    const value = params.value as any;
    const constantData = this.parseConstantValue(value);
    
    return {
      vertexShader: '',
      fragmentShader: 'constant',
      uniforms: { 
        ...params, 
        constantValue: constantData,
        hasFloatData: constantData && constantData.length > 0
      },
      inputTextures: [],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    const value = params.value as any;
    const constantData = this.parseConstantValue(value);
    
    if (constantData && constantData.length > 0) {
      const valueLocation = gl.getUniformLocation(program, 'u_constantValue');
      if (valueLocation && constantData.length >= 1) {
        // 对于灰度化模型，取前3个值作为RGB权重
        if (constantData.length >= 3) {
          gl.uniform3f(valueLocation, constantData[0] || 0, constantData[1] || 0, constantData[2] || 0);
        } else {
          gl.uniform1f(valueLocation, constantData[0] || 0);
        }
      }
    }
  }

  getOutputShape(inputShapes: number[][]): number[] {
    // 常量的输出形状根据值的形状确定
    return [1, 3, 1, 1]; // 默认形状，适用于灰度化权重
  }
  
  private parseConstantValue(value: any): Float32Array | null {
    if (!value) return null;
    
    // 处理不同格式的ONNX常量值
    if (value.floatData && value.floatData.length > 0) {
      return new Float32Array(value.floatData);
    }
    
    if (value.rawData && value.rawData.length > 0) {
      // 从原始字节数据解析float32
      const buffer = new ArrayBuffer(value.rawData.length);
      const view = new Uint8Array(buffer);
      view.set(value.rawData);
      return new Float32Array(buffer);
    }
    
    // 灰度化默认权重
    if (!value.floatData && !value.rawData) {
      return new Float32Array([0.299, 0.587, 0.114]); // RGB到灰度的标准权重
    }
    
    return null;
  }
}

/**
 * PRelu操作符实现
 */
class PReluOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'prelu',
      uniforms: params,
      inputTextures: ['u_input', 'u_slope'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // PRelu不需要额外的uniform设置
    // 斜率参数通过权重纹理传递
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || [];
  }
}

/**
 * 转置操作符实现
 */
class TransposeOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'transpose',
      uniforms: params,
      inputTextures: ['u_input'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // 获取转置排列，默认为[0,2,1,3]（NCHW -> NHWC）
    const perm = (params.perm as number[]) || [0, 2, 1, 3];
    
    const permLocation = gl.getUniformLocation(program, 'u_perm');
    if (permLocation && perm.length >= 4) {
      gl.uniform4i(permLocation, perm[0] || 0, perm[1] || 1, perm[2] || 2, perm[3] || 3);
    }
    
    const inputSizeLocation = gl.getUniformLocation(program, 'u_inputSize');
    if (inputSizeLocation) {
      gl.uniform2f(inputSizeLocation, 224.0, 224.0); // 默认尺寸
    }
  }

  getOutputShape(inputShapes: number[][]): number[] {
    if (!inputShapes[0] || inputShapes[0].length < 4) {
      return [1, 1, 224, 224];
    }
    
    const [N, C, H, W] = inputShapes[0];
    // 对于NCHW -> NHWC转换
    return [N || 1, H || 224, W || 224, C || 1];
  }
}

/**
 * 简化操作符实现 - 用于不支持的操作符
 */
class PassthroughOperator extends BaseOperatorImplementation {
  generateShader(params: Record<string, unknown>): ShaderOperation {
    return {
      vertexShader: '',
      fragmentShader: 'copy', // 使用现有的copy着色器
      uniforms: params,
      inputTextures: ['u_input'],
      outputTexture: 'output'
    };
  }

  setupUniforms(gl: WebGL2RenderingContext, program: WebGLProgram, params: Record<string, unknown>): void {
    // 简化实现，直接传递
    // 不需要额外的uniform设置
  }

  getOutputShape(inputShapes: number[][]): number[] {
    return inputShapes[0] || [];
  }
}

/**
 * 操作符映射器
 * 管理所有支持的操作符及其实现
 */
export class OperatorMapper {
  private operatorRegistry: Map<string, OperatorImplementation> = new Map();
  private shaderGenerator: ShaderGenerator;

  constructor(shaderGenerator: ShaderGenerator) {
    this.shaderGenerator = shaderGenerator;
    this.registerDefaultOperators();
  }

  /**
   * 注册默认操作符
   */
  private registerDefaultOperators(): void {
    // 基础操作符
    this.registerOperator('Relu', new ReluOperator());
    this.registerOperator('Conv', new Conv2DOperator());
    this.registerOperator('Conv2d', new Conv2DOperator());
    this.registerOperator('Add', new AddOperator());
    this.registerOperator('Mul', new MulOperator());
    this.registerOperator('Sub', new SubOperator());
    this.registerOperator('Constant', new ConstantOperator());
    
    // 其他常用操作符 - 使用特定实现
    this.registerOperator('Transpose', new TransposeOperator());
    this.registerOperator('PRelu', new PReluOperator());
    
    // 其他常用操作符 - 使用简化实现
    this.registerOperator('Concat', new PassthroughOperator());
    this.registerOperator('GlobalAveragePool', new PassthroughOperator());
    this.registerOperator('Resize', new PassthroughOperator());
    this.registerOperator('Softmax', new PassthroughOperator());
    this.registerOperator('Slice', new PassthroughOperator());
    this.registerOperator('MaxPool', new PassthroughOperator());
    this.registerOperator('MaxPool2d', new PassthroughOperator());
    this.registerOperator('BatchNormalization', new PassthroughOperator());
    this.registerOperator('Reshape', new PassthroughOperator());
  }

  /**
   * 注册操作符
   */
  public registerOperator(name: string, implementation: OperatorImplementation): void {
    this.operatorRegistry.set(name.toLowerCase(), implementation);
  }

  /**
   * 映射操作到着色器操作
   */
  public mapOperation(operationType: string, params: Record<string, unknown>): ShaderOperation {
    const implementation = this.operatorRegistry.get(operationType.toLowerCase());
    
    if (!implementation) {
      throw InferenceError.unsupportedOperator(operationType);
    }

    const operation = implementation.generateShader(params);
    
    // 使用着色器生成器生成实际的着色器代码
    operation.vertexShader = this.shaderGenerator.generateVertexShader();
    operation.fragmentShader = this.shaderGenerator.generateFragmentShader(operationType, params);

    return operation;
  }

  /**
   * 获取支持的操作符列表
   */
  public getSupportedOps(): string[] {
    return Array.from(this.operatorRegistry.keys());
  }

  /**
   * 验证图中的操作符支持情况
   */
  public validateSupport(operationTypes: string[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const opType of operationTypes) {
      if (!this.operatorRegistry.has(opType.toLowerCase())) {
        errors.push(`Unsupported operator: ${opType}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 设置操作符的uniform变量
   */
  public setupOperatorUniforms(
    operationType: string,
    gl: WebGL2RenderingContext,
    program: WebGLProgram,
    params: Record<string, unknown>
  ): void {
    const implementation = this.operatorRegistry.get(operationType.toLowerCase());
    
    if (implementation) {
      implementation.setupUniforms(gl, program, params);
    }
  }

  /**
   * 计算操作的输出形状
   */
  public getOutputShape(operationType: string, inputShapes: number[][]): number[] {
    const implementation = this.operatorRegistry.get(operationType.toLowerCase());
    
    if (!implementation) {
      throw InferenceError.unsupportedOperator(operationType);
    }

    return implementation.getOutputShape(inputShapes);
  }
}