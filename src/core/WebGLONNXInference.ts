import {
  IWebGLONNXInference,
  WebGLONNXInferenceConfig,
  ModelInfo,
  InferenceResult,
  PerformanceMetrics
} from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';
import { WebGLContextManager } from '../webgl/WebGLContextManager.js';
import { GPUInferenceEngine } from '../core/GPUInferenceEngine.js';

/**
 * WebGL ONNX推理库主接口
 * 提供完整的ONNX模型推理功能
 */
export class WebGLONNXInference implements IWebGLONNXInference {
  private contextManager: WebGLContextManager;
  private inferenceEngine: GPUInferenceEngine;
  private isInitialized = false;
  private performanceMetrics: PerformanceMetrics = {
    averageInferenceTime: 0,
    peakMemoryUsage: 0,
    texturePoolSize: 0,
    totalInferences: 0,
    fps: 0
  };
  private inferenceHistory: number[] = [];

  constructor() {
    // 默认不启用 debug 模式，在 initialize 方法中用实际配置覆盖
    this.contextManager = new WebGLContextManager(false);
    this.inferenceEngine = new GPUInferenceEngine(this.contextManager);
  }

  /**
   * 初始化推理引擎
   */
  public async initialize(config: WebGLONNXInferenceConfig): Promise<boolean> {
    try {
      // 如果配置了 debug 模式，重新创建 contextManager 和 inferenceEngine
      if (config.debugMode !== undefined) {
        this.contextManager = new WebGLContextManager(config.debugMode);
        this.inferenceEngine = new GPUInferenceEngine(this.contextManager);
      }

      // 初始化WebGL上下文
      const success = this.contextManager.initialize(config.canvas);
      if (!success) {
        return false;
      }

      // 初始化GPU推理引擎
      this.inferenceEngine.initialize();

      // 配置设置
      if (config.maxTextureSize) {
        const maxSize = this.contextManager.getMaxTextureSize();
        if (config.maxTextureSize > maxSize) {
          console.warn(`Requested texture size ${config.maxTextureSize} exceeds maximum ${maxSize}`);
        }
      }

      this.isInitialized = true;
      return true;

    } catch (error) {
      console.error('Failed to initialize WebGL ONNX Inference:', error);
      return false;
    }
  }

  /**
   * 加载ONNX模型
   */
  public async loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo> {
    this.ensureInitialized();

    try {
      const modelInfo = await this.inferenceEngine.loadModel(modelBuffer);
      return modelInfo;
    } catch (error) {
      if (error instanceof InferenceError) {
        throw error;
      }
      throw InferenceError.modelParseError('Failed to load model', error);
    }
  }

  /**
   * 设置输入纹理
   */
  public setInputTexture(texture: WebGLTexture): void {
    this.ensureInitialized();
    this.inferenceEngine.setInputTexture(texture);
  }

  /**
   * 从图片设置输入
   */
  public setInputFromImage(image: HTMLImageElement): void {
    this.ensureInitialized();
    this.inferenceEngine.setInputFromImage(image);
  }

  /**
   * 从Canvas设置输入
   */
  public setInputFromCanvas(canvas: HTMLCanvasElement): void {
    this.ensureInitialized();
    this.inferenceEngine.setInputFromCanvas(canvas);
  }

  /**
   * 从ImageData设置输入
   */
  public setInputFromImageData(imageData: ImageData): void {
    this.ensureInitialized();
    this.inferenceEngine.setInputFromImageData(imageData);
  }

  /**
   * 执行推理
   */
  public async inference(): Promise<InferenceResult> {
    this.ensureInitialized();

    try {
      const result = await this.inferenceEngine.execute();

      // 更新性能统计
      this.updatePerformanceMetrics(result.inferenceTime, result.memoryUsage);

      return result;
    } catch (error) {
      if (error instanceof InferenceError) {
        throw error;
      }
      throw InferenceError.inferenceExecutionError('Inference failed', error);
    }
  }

  /**
   * 获取输出纹理
   */
  public getOutputTexture(): WebGLTexture | null {
    if (!this.isInitialized) return null;
    return this.inferenceEngine.getOutputTexture();
  }

  /**
   * 获取输出为ImageData
   */
  public getOutputAsImageData(): ImageData | null {
    const outputTexture = this.getOutputTexture();
    if (!outputTexture) return null;

    // 简化实现：返回null，实际需要从纹理读取数据
    console.warn('getOutputAsImageData not fully implemented');
    return null;
  }

  /**
   * 获取输出为Float32Array
   */
  public getOutputAsFloat32Array(): Float32Array | null {
    const outputTexture = this.getOutputTexture();
    if (!outputTexture) return null;

    // 简化实现：返回null，实际需要从纹理读取数据
    console.warn('getOutputAsFloat32Array not fully implemented');
    return null;
  }

  /**
   * 渲染到Canvas
   */
  public renderToCanvas(canvas: HTMLCanvasElement): void {
    this.ensureInitialized();
    this.inferenceEngine.renderToCanvas(canvas);
  }

  /**
   * 检查渲染结果是否全黑或全白
   */
  public checkRenderResult(): { isAllBlack: boolean; isAllWhite: boolean; } {
    this.ensureInitialized();
    return this.inferenceEngine.checkRenderResult();
  }

  /**
   * 渲染到Canvas并检查结果
   */
  public renderToCanvasWithCheck(canvas: HTMLCanvasElement): { isAllBlack: boolean; isAllWhite: boolean; } {
    this.ensureInitialized();

    // 先渲染到Canvas
    this.inferenceEngine.renderToCanvas(canvas);

    // 然后检查渲染结果
    const result = this.inferenceEngine.checkRenderResult();

    // 如果结果是全黑或全白，抛出错误
    if (result.isAllBlack) {
      throw InferenceError.inferenceExecutionError('推理结果全黑，可能模型权重有问题');
    }

    if (result.isAllWhite) {
      throw InferenceError.inferenceExecutionError('推理结果全白，可能模型权重有问题');
    }

    return result;
  }

  /**
   * 获取中间结果用于可视化
   */
  public async getIntermediateResults(): Promise<Array<{ imageData: ImageData, label: string; }>> {
    this.ensureInitialized();
    return await this.inferenceEngine.getIntermediateResults();
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.inferenceEngine) {
      this.inferenceEngine.dispose();
    }

    if (this.contextManager) {
      this.contextManager.dispose();
    }

    this.isInitialized = false;
    this.inferenceHistory.length = 0;
  }

  /**
   * 确保已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw InferenceError.inferenceExecutionError('WebGL ONNX Inference not initialized');
    }
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceMetrics(inferenceTime: number, memoryUsage: number): void {
    this.performanceMetrics.totalInferences++;
    this.inferenceHistory.push(inferenceTime);

    // 保持最近100次推理记录
    if (this.inferenceHistory.length > 100) {
      this.inferenceHistory.shift();
    }

    // 计算平均推理时间
    this.performanceMetrics.averageInferenceTime =
      this.inferenceHistory.reduce((sum, time) => sum + time, 0) / this.inferenceHistory.length;

    // 更新峰值内存使用
    this.performanceMetrics.peakMemoryUsage = Math.max(
      this.performanceMetrics.peakMemoryUsage,
      memoryUsage
    );

    // 计算FPS（基于平均推理时间）
    this.performanceMetrics.fps = 1000 / this.performanceMetrics.averageInferenceTime;

    // 更新纹理池大小（需要从textureManager获取）
    this.performanceMetrics.texturePoolSize = 0; // 简化实现
  }
}

// 导出错误类型
export { InferenceError, InferenceErrorType } from '../errors/InferenceError.js';

// 导出类型定义
export * from '../types/index.js';