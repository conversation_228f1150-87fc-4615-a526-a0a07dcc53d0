import { WebGLContextManager } from '../webgl/WebGLContextManager.js';
import { TextureManager } from '../webgl/TextureManager.js';
import { ShaderGenerator } from '../shaders/ShaderGenerator.js';
import { OperatorMapper } from '../operators/OperatorMapper.js';
import { ONNXModelParser } from '../onnx/ONNXModelParser.js';
import { InferenceError } from '../errors/InferenceError.js';
import { ComputationGraph, OperationNode, ModelInfo, InferenceResult } from '../types/index.js';

/**
 * GPU推理引擎
 * 执行ONNX模型的GPU推理
 */
export class GPUInferenceEngine {
  private contextManager: WebGLContextManager;
  private textureManager: TextureManager;
  private shaderGenerator: ShaderGenerator;
  private operatorMapper: OperatorMapper;
  private modelParser: ONNXModelParser;

  // 固定尺寸配置
  private static readonly FIXED_SIZE = { width: 224, height: 224 };

  private currentGraph: ComputationGraph | null = null;
  private programs: Map<string, WebGLProgram> = new Map();
  private textures: Map<string, WebGLTexture> = new Map();
  private framebuffers: WebGLFramebuffer[] = [];
  private quadBuffer: WebGLBuffer | null = null;

  private inputTexture: WebGLTexture | null = null;
  private outputTexture: WebGLTexture | null = null;

  // 中间结果存储
  private intermediateResults: Array<{ texture: WebGLTexture, nodeId: string, nodeType: string; }> = [];

  constructor(contextManager: WebGLContextManager) {
    this.contextManager = contextManager;
    this.textureManager = new TextureManager(contextManager);
    this.shaderGenerator = new ShaderGenerator(contextManager);
    this.operatorMapper = new OperatorMapper(this.shaderGenerator);
    this.modelParser = new ONNXModelParser();

    // 不在构造函数中初始化QuadBuffer，等待WebGL上下文初始化后再调用
  }

  /**
   * 初始化GPU推理引擎
   */
  public initialize(): void {
    if (!this.contextManager.isReady()) {
      throw InferenceError.inferenceExecutionError('WebGL context not ready');
    }

    this.initializeQuadBuffer();
  }

  /**
   * 初始化全屏四边形缓冲区
   */
  private initializeQuadBuffer(): void {
    const gl = this.contextManager.getContext();

    // 全屏四边形顶点数据 (位置 + 纹理坐标)
    const vertices = new Float32Array([
      // 位置        纹理坐标
      -1.0, -1.0, 0.0, 0.0,
      1.0, -1.0, 1.0, 0.0,
      -1.0, 1.0, 0.0, 1.0,
      1.0, 1.0, 1.0, 1.0,
    ]);

    this.quadBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
  }

  /**
   * 加载ONNX模型
   */
  public async loadModel(modelBuffer: ArrayBuffer): Promise<ModelInfo> {
    try {
      await this.modelParser.loadModel(modelBuffer);
      this.currentGraph = this.modelParser.parseGraph();

      // 验证模型支持
      const operationTypes = this.currentGraph.nodes.map(node => node.type);
      const validation = this.operatorMapper.validateSupport(operationTypes);

      if (!validation.isValid) {
        throw InferenceError.modelParseError(
          `Unsupported operations: ${validation.errors.join(', ')}`
        );
      }

      // 预编译着色器程序
      await this.precompileShaders();

      // 初始化权重纹理
      this.initializeWeightTextures();

      return this.modelParser.getModelInfo();

    } catch (error) {
      if (error instanceof InferenceError) {
        throw error;
      }
      throw InferenceError.modelParseError('Failed to load model', error);
    }
  }

  /**
   * 预编译着色器程序
   */
  private async precompileShaders(): Promise<void> {
    if (!this.currentGraph) return;

    for (const node of this.currentGraph.nodes) {
      try {
        console.log(`正在为节点 ${node.id} (类型: ${node.type}) 编译着色器...`);
        const program = this.shaderGenerator.compileShaderProgram(node.type, node.attributes);
        this.programs.set(node.id, program);
        console.log(`✅ 节点 ${node.id} 的着色器编译成功`);
      } catch (error) {
        console.error(`❌ 节点 ${node.id} (类型: ${node.type}) 着色器编译失败:`, error);
        // 对于关键操作符，抛出错误而不是忽略
        if (['sub', 'add', 'mul', 'conv', 'conv2d'].includes(node.type.toLowerCase())) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          throw InferenceError.shaderCompilationError(`${node.type}`, `Failed to compile shader for ${node.type}: ${errorMessage}`);
        }
      }
    }
  }

  /**
   * 初始化权重纹理
   */
  private initializeWeightTextures(): void {
    if (!this.currentGraph) return;

    console.log('开始初始化权重纹理...');

    // 首先清理之前的权重纹理
    this.textureManager.clearWeightCache();

    // 使用模型解析器获取详细权重信息
    for (const [name, weights] of this.currentGraph.initializers.entries()) {
      try {
        // 获取权重完整信息包括形状
        const weightInfo = this.modelParser.getWeightInfo(name);
        if (weightInfo) {
          const texture = this.textureManager.createWeightTexture(
            name,
            weightInfo.data,
            weightInfo.shape
          );
          this.textures.set(name, texture);
        } else {
          // 备用方案：使用简单权重创建
          this.createSimpleWeightTexture(name, weights);
        }

        console.log(`初始化权重纹理: ${name}, 数据长度: ${weights.length}`);
      } catch (error) {
        console.warn(`初始化权重纹理失败: ${name}`, error);
        // 备用方案
        this.createSimpleWeightTexture(name, weights);
      }
    }

    console.log(`权重纹理初始化完成，共 ${this.textures.size} 个`);
  }

  /**
   * 创建简单权重纹理（备用方案）
   */
  private createSimpleWeightTexture(name: string, weights: Float32Array): void {
    try {
      if (weights.length <= 4) {
        // 小的权重数组（如RGB灰度化权重）
        const paddedData = new Float32Array(4);
        for (let i = 0; i < Math.min(weights.length, 4); i++) {
          paddedData[i] = weights[i] || 0;
        }
        const texture = this.textureManager.createFloat32Texture(1, 1, paddedData);
        this.textures.set(name, texture);
      } else {
        // 大的权重数组，转换为2D纹理
        const size = Math.ceil(Math.sqrt(weights.length / 4));
        const paddedData = new Float32Array(size * size * 4);
        paddedData.set(weights);

        const texture = this.textureManager.createFloat32Texture(size, size, paddedData);
        this.textures.set(name, texture);
      }
    } catch (error) {
      console.error(`备用权重纹理创建失败: ${name}`, error);
    }
  }

  /**
   * 设置输入纹理
   */
  public setInputTexture(texture: WebGLTexture): void {
    this.inputTexture = texture;
  }

  /**
   * 从图片设置输入
   */
  public setInputFromImage(image: HTMLImageElement): void {
    this.ensureInitialized();
    
    const drawCallback = (canvas: HTMLCanvasElement) => {
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('无法获取Canvas 2D上下文');
      
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // 计算缩放比例以保持宽高比并填满整个canvas
      const scaleX = canvas.width / image.width;
      const scaleY = canvas.height / image.height;
      const scale = Math.min(scaleX, scaleY);
      
      // 计算居中位置
      const scaledWidth = image.width * scale;
      const scaledHeight = image.height * scale;
      const x = (canvas.width - scaledWidth) / 2;
      const y = (canvas.height - scaledHeight) / 2;
      
      // 绘制图像，保持宽高比并居中
      ctx.drawImage(image, x, y, scaledWidth, scaledHeight);
    };
    
    this.inputTexture = this.createInputTexture(drawCallback, image.width, image.height);
  }

  /**
   * 从Canvas设置输入
   */
  public setInputFromCanvas(canvas: HTMLCanvasElement): void {
    this.inputTexture = this.createResizedInputTexture(canvas.width, canvas.height, (targetCanvas) => {
      const ctx = targetCanvas.getContext('2d');
      if (!ctx) throw new Error('无法获取Canvas 2D上下文');
      ctx.drawImage(canvas, 0, 0, targetCanvas.width, targetCanvas.height);
    });
  }

  /**
   * 从ImageData设置输入
   */
  public setInputFromImageData(imageData: ImageData): void {
    this.inputTexture = this.createResizedInputTexture(imageData.width, imageData.height, (canvas) => {
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('无法获取Canvas 2D上下文');
      ctx.putImageData(imageData, 0, 0);
      // 如果需要缩放，重新绘制到正确尺寸
      if (imageData.width !== canvas.width || imageData.height !== canvas.height) {
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = imageData.width;
        tempCanvas.height = imageData.height;
        const tempCtx = tempCanvas.getContext('2d');
        if (!tempCtx) throw new Error('无法获取临时Canvas 2D上下文');
        tempCtx.putImageData(imageData, 0, 0);
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(tempCanvas, 0, 0, canvas.width, canvas.height);
      }
    });
  }

  /**
   * 创建调整尺寸后的输入纹理
   */
  private createResizedInputTexture(
    originalWidth: number, 
    originalHeight: number, 
    drawCallback: (canvas: HTMLCanvasElement) => void
  ): WebGLTexture {
    const fixedSize = GPUInferenceEngine.FIXED_SIZE;
    
    // 如果原始尺寸已经是固定尺寸，直接创建纹理
    if (originalWidth === fixedSize.width && originalHeight === fixedSize.height) {
      const canvas = document.createElement('canvas');
      canvas.width = originalWidth;
      canvas.height = originalHeight;
      drawCallback(canvas);
      return this.textureManager.createFromCanvas(canvas);
    }
    
    // 否则创建调整尺寸后的纹理
    const resizedCanvas = document.createElement('canvas');
    resizedCanvas.width = fixedSize.width;
    resizedCanvas.height = fixedSize.height;
    
    const ctx = resizedCanvas.getContext('2d');
    if (!ctx) throw new Error('无法获取调整尺寸后的Canvas 2D上下文');
    
    // 使用高质量图像缩放
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // 绘制调整尺寸后的图像
    drawCallback(resizedCanvas);
    
    console.log(`输入纹理已从 ${originalWidth}x${originalHeight} 降采样到 ${fixedSize.width}x${fixedSize.height}`);
    
    return this.textureManager.createFromCanvas(resizedCanvas);
  }

  /**
   * 执行推理
   */
  public async execute(): Promise<InferenceResult> {
    const startTime = performance.now();

    if (!this.currentGraph) {
      throw InferenceError.inferenceExecutionError('No model loaded');
    }

    if (!this.inputTexture) {
      throw InferenceError.inferenceExecutionError('No input texture set');
    }

    try {
      // 执行计算图
      this.outputTexture = await this.executeGraph();

      const endTime = performance.now();
      const inferenceTime = endTime - startTime;

      return {
        outputTexture: this.outputTexture,
        inferenceTime,
        memoryUsage: this.textureManager.getMemoryUsage() / (1024 * 1024) // MB
      };

    } catch (error) {
      if (error instanceof InferenceError) {
        throw error;
      }
      throw InferenceError.inferenceExecutionError('Inference failed', error);
    }
  }

  /**
   * 执行计算图
   */
  private async executeGraph(): Promise<WebGLTexture> {
    if (!this.currentGraph) {
      throw InferenceError.inferenceExecutionError('No graph available');
    }

    const gl = this.contextManager.getContext();
    const nodeOutputs: Map<string, WebGLTexture> = new Map();

    // 清理之前的中间结果
    this.clearIntermediateResults();

    // 设置初始输入
    if (this.currentGraph.inputs.length > 0 && this.currentGraph.inputs[0]) {
      nodeOutputs.set(this.currentGraph.inputs[0], this.inputTexture!);
    }

    // 按序执行节点
    for (const node of this.currentGraph.nodes) {
      const outputTexture = await this.executeNode(node, nodeOutputs);

      // 存储节点输出
      if (node.outputs.length > 0 && node.outputs[0]) {
        nodeOutputs.set(node.outputs[0], outputTexture);
      }
    }

    // 返回最终输出
    const finalOutputName = this.currentGraph.outputs[0];
    if (!finalOutputName) {
      throw InferenceError.inferenceExecutionError('No output defined in graph');
    }

    const finalOutput = nodeOutputs.get(finalOutputName);

    if (!finalOutput) {
      throw InferenceError.inferenceExecutionError('Failed to get final output');
    }

    return finalOutput;
  }

  /**
   * 执行单个节点
   */
  private async executeNode(
    node: OperationNode,
    nodeOutputs: Map<string, WebGLTexture>
  ): Promise<WebGLTexture> {
    const gl = this.contextManager.getContext();

    console.log(`执行节点: ${node.id} (类型: ${node.type})`);

    // 获取输入纹理（排除权重）
    const inputTextures: WebGLTexture[] = [];
    for (const inputName of node.inputs) {
      // 优先从节点输出中查找（实际数据流）
      const inputTexture = nodeOutputs.get(inputName);
      if (inputTexture) {
        inputTextures.push(inputTexture);
        console.log(`使用节点输入: ${inputName}`);
      } else if (!this.textures.has(inputName)) {
        // 只有在不是权重的情况下才警告
        console.warn(`未找到输入: ${inputName}`);
      }
    }

    // 对于常量操作，不需要输入纹理
    if (node.type.toLowerCase() === 'constant') {
      console.log('执行常量操作');
    } else if (inputTextures.length === 0 && node.inputs.length > 0) {
      // 尝试使用默认输入
      if (this.inputTexture && node.inputs.includes('input')) {
        inputTextures.push(this.inputTexture);
        console.log('使用默认输入纹理');
      }
    }

    // 获取输入纹理尺寸（使用固定尺寸）
    const inputSize = GPUInferenceEngine.FIXED_SIZE;

    // 创建输出纹理
    const outputTexture = this.textureManager.createFloat32Texture(inputSize.width, inputSize.height);

    // 获取着色器程序
    const program = this.programs.get(node.id);
    if (!program) {
      throw InferenceError.inferenceExecutionError(`No program for node ${node.id}`);
    }

    // 创建帧缓冲区
    const framebuffer = this.contextManager.createFramebuffer();
    this.contextManager.bindFramebufferTexture(framebuffer, outputTexture);

    // 设置视口
    gl.viewport(0, 0, inputSize.width, inputSize.height);

    // 使用着色器程序
    gl.useProgram(program);

    // 绑定输入纹理
    this.bindInputTextures(gl, program, inputTextures, node);

    // 设置uniform参数
    this.operatorMapper.setupOperatorUniforms(node.type, gl, program, node.attributes);
    this.setupStandardUniforms(gl, program, inputSize);

    // 设置顶点属性
    this.setupVertexAttributes(gl, program);

    // 绘制
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

    // 清理
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.deleteFramebuffer(framebuffer);

    this.contextManager.checkError(`executeNode-${node.type}`);

    // 检查中间结果是否全黑或全白（仅在debug模式下）
    if (this.contextManager.isDebugMode()) {
      const checkResult = this.checkTextureResult(outputTexture, node.id);
      if (checkResult.isAllBlack) {
        console.warn(`警告: 节点 ${node.id} 输出全黑`);
      } else if (checkResult.isAllWhite) {
        console.warn(`警告: 节点 ${node.id} 输出全白`);
      }

      // 收集中间结果用于可视化
      this.intermediateResults.push({
        texture: outputTexture,
        nodeId: node.id,
        nodeType: node.type
      });

      // 添加更详细的调试信息
      this.logTextureStats(outputTexture, node.id, node.type);
    }

    console.log(`节点 ${node.id} 执行完成`);
    return outputTexture;
  }

  /**
   * 绑定输入纹理
   */
  private bindInputTextures(
    gl: WebGL2RenderingContext,
    program: WebGLProgram,
    textures: WebGLTexture[],
    node: OperationNode
  ): void {
    // 根据操作类型确定纹理名称
    let textureNames: string[];

    switch (node.type.toLowerCase()) {
      case 'conv':
      case 'conv2d':
        textureNames = ['u_input', 'u_weights']; // 更新为u_weights
        break;
      case 'add':
        textureNames = ['u_input', 'u_bias'];
        break;
      case 'mul':
        textureNames = ['u_inputA', 'u_inputB'];
        break;
      case 'constant':
        textureNames = []; // 常量操作不需要输入纹理
        break;
      default:
        textureNames = ['u_input'];
        break;
    }

    let textureUnit = 0;

    // 绑定输入纹理
    for (let i = 0; i < Math.min(textures.length, textureNames.length); i++) {
      const textureName = textureNames[i];
      const texture = textures[i];
      if (textureName && texture) {
        const location = gl.getUniformLocation(program, textureName);
        if (location) {
          gl.activeTexture(gl.TEXTURE0 + textureUnit);
          gl.bindTexture(gl.TEXTURE_2D, texture);
          gl.uniform1i(location, textureUnit);
          textureUnit++;

          console.log(`绑定纹理 ${textureName} 到单元 ${textureUnit - 1}`);
        }
      }
    }

    // 绑定权重纹理（如果有）
    if (node.weights && node.weights.length > 0) {
      for (const weightName of node.weights) {
        const weightTexture = this.textures.get(weightName);
        if (weightTexture) {
          // 根据权重类型确定纹理名称
          let uniformName = 'u_weights';
          if (node.type.toLowerCase() === 'conv' || node.type.toLowerCase() === 'conv2d') {
            uniformName = 'u_weights';
          } else if (node.type.toLowerCase() === 'add') {
            uniformName = 'u_bias';
          }

          const location = gl.getUniformLocation(program, uniformName);
          if (location) {
            gl.activeTexture(gl.TEXTURE0 + textureUnit);
            gl.bindTexture(gl.TEXTURE_2D, weightTexture);
            gl.uniform1i(location, textureUnit);
            textureUnit++;

            console.log(`绑定权重纹理 ${weightName} -> ${uniformName} 到单元 ${textureUnit - 1}`);
          }
        }
      }
    }
  }

  /**
   * 设置标准uniform变量
   */
  private setupStandardUniforms(
    gl: WebGL2RenderingContext,
    program: WebGLProgram,
    size: { width: number; height: number; }
  ): void {
    const inputSizeLocation = gl.getUniformLocation(program, 'u_inputSize');
    if (inputSizeLocation) {
      gl.uniform2f(inputSizeLocation, size.width, size.height);
    }
  }

  /**
   * 设置顶点属性
   */
  private setupVertexAttributes(gl: WebGL2RenderingContext, program: WebGLProgram): void {
    if (!this.quadBuffer) return;

    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);

    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');

    if (positionLocation >= 0) {
      gl.enableVertexAttribArray(positionLocation);
      gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
    }

    if (texCoordLocation >= 0) {
      gl.enableVertexAttribArray(texCoordLocation);
      gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
    }
  }

  /**
   * 获取输出纹理
   */
  public getOutputTexture(): WebGLTexture | null {
    return this.outputTexture;
  }

  /**
   * 渲染到Canvas
   */
  public renderToCanvas(canvas: HTMLCanvasElement): void {
    if (!this.outputTexture) {
      throw InferenceError.inferenceExecutionError('No output texture available');
    }

    const gl = this.contextManager.getContext();

    // 创建灰度转换程序
    const grayscaleProgram = this.shaderGenerator.compileShaderProgram('grayscale');

    // 设置Canvas为渲染目标
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.viewport(0, 0, canvas.width, canvas.height);

    // 使用灰度着色器
    gl.useProgram(grayscaleProgram);

    // 绑定输出纹理
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, this.outputTexture);
    const inputLocation = gl.getUniformLocation(grayscaleProgram, 'u_input');
    if (inputLocation) gl.uniform1i(inputLocation, 0);

    // 设置顶点属性并绘制
    this.setupVertexAttributes(gl, grayscaleProgram);
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

    this.contextManager.checkError('renderToCanvas');
  }

  /**
   * 检查渲染结果是否全黑或全白
   */
  public checkRenderResult(): { isAllBlack: boolean; isAllWhite: boolean; } {
    if (!this.outputTexture) {
      throw InferenceError.inferenceExecutionError('No output texture available');
    }

    const gl = this.contextManager.getContext();

    // 渲染结果检查也使用固定尺寸（与中间结果保持一致）
    const width = GPUInferenceEngine.FIXED_SIZE.width;
    const height = GPUInferenceEngine.FIXED_SIZE.height;
    console.log(`渲染结果检查使用固定尺寸: ${width}x${height}`);

    // 创建临时帧缓冲区
    const framebuffer = this.contextManager.createFramebuffer();
    this.contextManager.bindFramebufferTexture(framebuffer, this.outputTexture);

    // 读取像素数据
    const pixels = new Uint8Array(width * height * 4);
    gl.readPixels(0, 0, width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels);

    // 清理
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.deleteFramebuffer(framebuffer);

    this.contextManager.checkError('checkRenderResult');

    // 分析像素数据
    let allBlack = true;
    let allWhite = true;
    let hasNonTransparentPixels = false;

    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i] as number;
      const g = pixels[i + 1] as number;
      const b = pixels[i + 2] as number;
      const a = pixels[i + 3] as number;

      // 检查是否为非透明像素
      if (a > 0) {
        hasNonTransparentPixels = true;

        // 检查是否为黑色 (0,0,0)
        if (r > 5 || g > 5 || b > 5) {
          allBlack = false;
        }

        // 检查是否为白色 (255,255,255)
        if (r < 250 || g < 250 || b < 250) {
          allWhite = false;
        }

        // 如果既不是全黑也不是全白，且已经有非透明像素，可以提前退出
        if (!allBlack && !allWhite && hasNonTransparentPixels) {
          break;
        }
      }
    }

    // 如果没有非透明像素，认为既不是全黑也不是全白
    if (!hasNonTransparentPixels) {
      return { isAllBlack: false, isAllWhite: false };
    }

    return { isAllBlack: allBlack, isAllWhite: allWhite };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    const gl = this.contextManager.getContext();

    // 清理程序
    for (const program of this.programs.values()) {
      gl.deleteProgram(program);
    }
    this.programs.clear();

    // 清理纹理
    this.textureManager.cleanup();
    this.textures.clear();

    // 清理缓冲区
    if (this.quadBuffer) {
      gl.deleteBuffer(this.quadBuffer);
      this.quadBuffer = null;
    }

    // 清理帧缓冲区
    for (const fb of this.framebuffers) {
      gl.deleteFramebuffer(fb);
    }
    this.framebuffers.length = 0;

    // 清理其他组件
    this.shaderGenerator.dispose();
    this.modelParser.dispose();

    this.currentGraph = null;
    this.inputTexture = null;
    this.outputTexture = null;
  }

  /**
   * 记录纹理统计信息用于调试
   */
  private logTextureStats(texture: WebGLTexture, nodeId: string, nodeType: string): void {
    if (!this.contextManager.isDebugMode()) {
      return;
    }

    const gl = this.contextManager.getContext();

    // 所有纹理统计使用固定尺寸
    const width = GPUInferenceEngine.FIXED_SIZE.width;
    const height = GPUInferenceEngine.FIXED_SIZE.height;
    console.log(`纹理统计 [${nodeType}-${nodeId}] 使用固定尺寸: ${width}x${height}`);

    // 创建临时帧缓冲区
    const framebuffer = this.contextManager.createFramebuffer();
    this.contextManager.bindFramebufferTexture(framebuffer, texture);

    // 读取像素数据
    const pixels = new Float32Array(width * height * 4);
    gl.readPixels(0, 0, width, height, gl.RGBA, gl.FLOAT, pixels);

    // 清理
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.deleteFramebuffer(framebuffer);

    // 计算统计信息
    let minValue = Infinity;
    let maxValue = -Infinity;
    let sum = 0;
    let nonZeroCount = 0;

    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i] as number;
      if (Math.abs(r) > 1e-12) {
        nonZeroCount++;
        minValue = Math.min(minValue, r);
        maxValue = Math.max(maxValue, r);
        sum += r;
      }
    }

    const mean = nonZeroCount > 0 ? sum / nonZeroCount : 0;

    // 计算标准差
    let sumSquares = 0;
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i] as number;
      if (Math.abs(r) > 1e-12) {
        sumSquares += (r - mean) * (r - mean);
      }
    }
    const stdDev = nonZeroCount > 0 ? Math.sqrt(sumSquares / nonZeroCount) : 0;

    console.log(`纹理统计 [${nodeType}-${nodeId}]: 
      最小值: ${minValue.toFixed(6)}, 
      最大值: ${maxValue.toFixed(6)}, 
      均值: ${mean.toFixed(6)}, 
      标准差: ${stdDev.toFixed(6)}, 
      非零像素: ${nonZeroCount}/${pixels.length / 4} (${(nonZeroCount / (pixels.length / 4) * 100).toFixed(2)}%)`);
  }

  /**
   * 检查纹理结果是否全黑或全白
   */
  private checkTextureResult(texture: WebGLTexture, nodeLabel: string): { isAllBlack: boolean; isAllWhite: boolean; } {
    const gl = this.contextManager.getContext();

    // 所有纹理检查使用固定尺寸
    const width = GPUInferenceEngine.FIXED_SIZE.width;
    const height = GPUInferenceEngine.FIXED_SIZE.height;
    console.log(`纹理检查 [${nodeLabel}] 使用固定尺寸: ${width}x${height}`);

    // 创建临时帧缓冲区
    const framebuffer = this.contextManager.createFramebuffer();
    this.contextManager.bindFramebufferTexture(framebuffer, texture);

    // 读取像素数据
    const pixels = new Float32Array(width * height * 4);
    gl.readPixels(0, 0, width, height, gl.RGBA, gl.FLOAT, pixels);

    // 清理
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.deleteFramebuffer(framebuffer);

    this.contextManager.checkError(`checkTextureResult-${nodeLabel}`);

    // 分析像素数据
    let allBlack = true;
    let allWhite = true;
    let hasNonZeroPixels = false;
    let nonZeroCount = 0;

    // 统计非零像素
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i] as number;
      const g = pixels[i + 1] as number;
      const b = pixels[i + 2] as number;
      const a = pixels[i + 3] as number;

      // 检查是否为非零像素（使用更宽松的阈值）
      if (Math.abs(r) > 1e-10 || Math.abs(g) > 1e-10 || Math.abs(b) > 1e-10 || Math.abs(a) > 1e-10) {
        hasNonZeroPixels = true;
        nonZeroCount++;

        // 检查是否为黑色 (接近0) - 使用更宽松的阈值
        if (Math.abs(r) > 1e-6 || Math.abs(g) > 1e-6 || Math.abs(b) > 1e-6) {
          allBlack = false;
        }

        // 检查是否为白色 (接近1) - 使用更宽松的阈值
        if (Math.abs(r - 1.0) > 1e-6 || Math.abs(g - 1.0) > 1e-6 || Math.abs(b - 1.0) > 1e-6) {
          allWhite = false;
        }

        // 如果既不是全黑也不是全白，且已经有足够多的非零像素，可以提前退出
        if (!allBlack && !allWhite && nonZeroCount > 10) {
          break;
        }
      }
    }

    // 如果没有非零像素，认为既不是全黑也不是全白
    if (!hasNonZeroPixels || nonZeroCount < 10) {
      return { isAllBlack: false, isAllWhite: false };
    }

    return { isAllBlack: allBlack, isAllWhite: allWhite };
  }

  /**
   * 获取中间结果用于可视化
   */
  public async getIntermediateResults(): Promise<Array<{ imageData: ImageData, label: string; }>> {
    if (!this.contextManager.isDebugMode() || this.intermediateResults.length === 0) {
      return [];
    }

    const gl = this.contextManager.getContext();
    const results: Array<{ imageData: ImageData, label: string; }> = [];

    // 为每个中间结果创建图像数据
    for (const intermediate of this.intermediateResults) {
      try {
        // 获取纹理信息
        const textureInfo = this.textureManager.getTextureInfo(intermediate.texture);
        let width = 224;
        let height = 224;
        
        // 所有中间结果都使用固定尺寸
        width = GPUInferenceEngine.FIXED_SIZE.width;
        height = GPUInferenceEngine.FIXED_SIZE.height;
        console.log(`中间结果 [${intermediate.nodeType}-${intermediate.nodeId}] 使用固定尺寸: ${width}x${height}`);

        // 创建帧缓冲区并绑定纹理
        const framebuffer = this.contextManager.createFramebuffer();
        this.contextManager.bindFramebufferTexture(framebuffer, intermediate.texture);

        // 读取浮点数据
        const floatPixels = new Float32Array(width * height * 4);
        gl.readPixels(0, 0, width, height, gl.RGBA, gl.FLOAT, floatPixels);

        // 转换为RGBA字节数据（进一步改进浮点到字节的转换）
        const bytePixels = new Uint8ClampedArray(width * height * 4);

        // 计算非零值的统计信息，用于更好的可视化
        let minValue = Infinity;
        let maxValue = -Infinity;
        let hasNonZero = false;
        let nonZeroCount = 0;
        let sum = 0;
        let sumSquares = 0;

        // 第一次遍历：计算统计信息
        for (let i = 0; i < floatPixels.length; i += 4) {
          const r = floatPixels[i] as number;
          if (Math.abs(r) > 1e-12) { // 更敏感的非零检测
            hasNonZero = true;
            nonZeroCount++;
            minValue = Math.min(minValue, r);
            maxValue = Math.max(maxValue, r);
            sum += r;
            sumSquares += r * r;
          }
        }

        // 如果没有足够的非零值，使用默认范围
        if (!hasNonZero || nonZeroCount < Math.max(10, width * height * 0.01)) { // 至少需要1%的非零像素
          minValue = 0;
          maxValue = 1;
          nonZeroCount = width * height;
        }

        // 计算均值和标准差
        const mean = nonZeroCount > 0 ? sum / nonZeroCount : 0;
        const variance = nonZeroCount > 0 ? (sumSquares / nonZeroCount) - (mean * mean) : 0;
        const stdDev = Math.sqrt(Math.max(0, variance));

        // 如果标准差为0或非常小，使用min-max范围
        const useMinMax = stdDev < 1e-10 || Math.abs(maxValue - minValue) < 1e-10;

        console.log(`可视化处理 [${intermediate.nodeType}-${intermediate.nodeId}]: 
          原始范围: [${minValue.toFixed(6)}, ${maxValue.toFixed(6)}], 
          均值: ${mean.toFixed(6)}, 
          标准差: ${stdDev.toFixed(6)}, 
          非零像素: ${nonZeroCount}/${width * height}`);

        // 转换浮点值到0-255范围
        for (let i = 0; i < floatPixels.length; i += 4) {
          // 对于灰度图像，我们使用R通道值
          let value = floatPixels[i] as number;

          // 处理接近零的值
          if (Math.abs(value) < 1e-12) {
            value = 0;
          }
          // 对于非常小但非零的值，使用增强的可视化
          else if (Math.abs(value) < 1e-6) {
            // 使用立方根缩放增强极小值的可视化效果
            const sign = Math.sign(value);
            value = sign * Math.pow(Math.abs(value) * 1e6, 1 / 3) / 10;
          }
          // 如果标准差有效且值在均值几个标准差范围内，使用标准化
          else if (!useMinMax && stdDev > 1e-10 && Math.abs(value - mean) <= 3 * stdDev) {
            // 标准化到-1到1范围
            value = (value - mean) / (3 * stdDev); // 使用3个标准差范围
            // 转换到0-1范围
            value = (value + 1) / 2;
          }
          // 否则使用min-max标准化
          else {
            // 防止除零错误
            const range = Math.max(1e-10, maxValue - minValue);
            value = (value - minValue) / range;
          }

          // 确保值在0-1范围内
          value = Math.max(0, Math.min(1, value));

          // 转换为0-255范围的字节值
          const byteValue = Math.round(value * 255);
          bytePixels[i] = byteValue;     // R
          bytePixels[i + 1] = byteValue; // G
          bytePixels[i + 2] = byteValue; // B
          bytePixels[i + 3] = 255;       // A
        }

        // 创建ImageData
        const imageData = new ImageData(bytePixels, width, height);
        results.push({
          imageData: imageData,
          label: `${intermediate.nodeType}-${intermediate.nodeId}`
        });

        // 清理
        gl.bindFramebuffer(gl.FRAMEBUFFER, null);
        gl.deleteFramebuffer(framebuffer);

      } catch (error) {
        console.warn(`无法获取中间结果 ${intermediate.nodeId}:`, error);
      }
    }

    return results;
  }

  /**
   * 清理中间结果
   */
  private clearIntermediateResults(): void {
    this.intermediateResults = [];
  }
}
