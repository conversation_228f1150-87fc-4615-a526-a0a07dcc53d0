import { TextureSpec } from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';
import { WebGLContextManager } from './WebGLContextManager.js';

/**
 * 纹理池管理器
 * 提供纹理的创建、复用和内存管理功能
 */
export class TextureManager {
  private contextManager: WebGLContextManager;
  private availableTextures: Map<string, WebGLTexture[]> = new Map();
  private usedTextures: Set<WebGLTexture> = new Set();
  private textureSpecs: Map<WebGLTexture, TextureSpec> = new Map();
  private weightTextures: Map<string, WebGLTexture> = new Map(); // 权重纹理缓存
  private totalMemoryUsage = 0;
  private maxPoolSize = 50; // 最大池化纹理数量

  constructor(contextManager: WebGLContextManager) {
    this.contextManager = contextManager;
  }

  /**
   * 获取纹理规格键
   */
  private getSpecKey(spec: Omit<TextureSpec, 'size'>): string {
    return `${spec.width}x${spec.height}_${spec.format}_${spec.type}`;
  }

  /**
   * 计算纹理内存大小
   */
  private calculateTextureSize(width: number, height: number, format: number, type: number): number {
    const gl = this.contextManager.getContext();

    let bytesPerPixel = 4; // 默认RGBA

    // 根据格式计算字节数
    if (format === gl.RGBA) {
      if (type === gl.UNSIGNED_BYTE) {
        bytesPerPixel = 4;
      } else if (type === gl.FLOAT) {
        bytesPerPixel = 16; // 4 * 4 bytes
      } else if (type === gl.HALF_FLOAT) {
        bytesPerPixel = 8; // 4 * 2 bytes
      }
    } else if (format === gl.RED) {
      if (type === gl.UNSIGNED_BYTE) {
        bytesPerPixel = 1;
      } else if (type === gl.FLOAT) {
        bytesPerPixel = 4;
      } else if (type === gl.HALF_FLOAT) {
        bytesPerPixel = 2;
      }
    }

    return width * height * bytesPerPixel;
  }

  /**
   * 获取纹理 - 优先从池中复用
   */
  public acquire(
    width: number,
    height: number,
    format: number,
    internalFormat: number,
    type: number,
    data?: ArrayBufferView | null
  ): WebGLTexture {
    const spec: Omit<TextureSpec, 'size'> = {
      width,
      height,
      format,
      type,
      internalFormat
    };

    const specKey = this.getSpecKey(spec);
    const availableList = this.availableTextures.get(specKey) || [];

    let texture: WebGLTexture;

    // 尝试从池中获取可复用的纹理
    if (availableList.length > 0) {
      texture = availableList.pop()!;

      // 如果需要上传新数据，更新纹理内容
      if (data !== undefined) {
        this.updateTextureData(texture, width, height, format, type, data);
      }
    } else {
      // 创建新纹理
      texture = this.contextManager.createTexture(
        width,
        height,
        format,
        internalFormat,
        type,
        data
      );

      // 记录纹理规格
      const size = this.calculateTextureSize(width, height, format, type);
      const fullSpec: TextureSpec = { ...spec, size };
      this.textureSpecs.set(texture, fullSpec);
      this.totalMemoryUsage += size;
    }

    // 标记为使用中
    this.usedTextures.add(texture);

    return texture;
  }

  /**
   * 更新纹理数据
   */
  private updateTextureData(
    texture: WebGLTexture,
    width: number,
    height: number,
    format: number,
    type: number,
    data: ArrayBufferView | null
  ): void {
    const gl = this.contextManager.getContext();

    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texSubImage2D(
      gl.TEXTURE_2D,
      0,
      0, 0, // offset
      width,
      height,
      format,
      type,
      data
    );
    gl.bindTexture(gl.TEXTURE_2D, null);

    this.contextManager.checkError('updateTextureData');
  }

  /**
   * 释放纹理 - 返回到池中复用
   */
  public release(texture: WebGLTexture): void {
    if (!this.usedTextures.has(texture)) {
      console.warn('Attempting to release texture that is not in use');
      return;
    }

    this.usedTextures.delete(texture);

    const spec = this.textureSpecs.get(texture);
    if (!spec) {
      console.warn('Texture spec not found, deleting texture');
      this.deleteTexture(texture);
      return;
    }

    const specKey = this.getSpecKey(spec);

    // 检查池大小限制
    const availableList = this.availableTextures.get(specKey) || [];
    if (availableList.length >= this.maxPoolSize) {
      // 池已满，直接删除
      this.deleteTexture(texture);
      return;
    }

    // 返回到池中
    availableList.push(texture);
    this.availableTextures.set(specKey, availableList);
  }

  /**
   * 删除纹理
   */
  private deleteTexture(texture: WebGLTexture): void {
    const gl = this.contextManager.getContext();
    gl.deleteTexture(texture);

    const spec = this.textureSpecs.get(texture);
    if (spec) {
      this.totalMemoryUsage -= spec.size;
      this.textureSpecs.delete(texture);
    }
  }

  /**
   * 从图片创建纹理
   */
  public createFromImage(image: HTMLImageElement): WebGLTexture {
    const gl = this.contextManager.getContext();

    const texture = this.acquire(
      image.width,
      image.height,
      gl.RGBA,
      gl.RGBA,
      gl.UNSIGNED_BYTE
    );

    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(
      gl.TEXTURE_2D,
      0,
      gl.RGBA,
      gl.RGBA,
      gl.UNSIGNED_BYTE,
      image
    );
    gl.bindTexture(gl.TEXTURE_2D, null);

    this.contextManager.checkError('createFromImage');

    return texture;
  }

  /**
   * 从Canvas创建纹理
   */
  public createFromCanvas(canvas: HTMLCanvasElement): WebGLTexture {
    const gl = this.contextManager.getContext();

    const texture = this.acquire(
      canvas.width,
      canvas.height,
      gl.RGBA,
      gl.RGBA,
      gl.UNSIGNED_BYTE
    );

    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(
      gl.TEXTURE_2D,
      0,
      gl.RGBA,
      gl.RGBA,
      gl.UNSIGNED_BYTE,
      canvas
    );
    gl.bindTexture(gl.TEXTURE_2D, null);

    this.contextManager.checkError('createFromCanvas');

    return texture;
  }

  /**
   * 从ImageData创建纹理
   */
  public createFromImageData(imageData: ImageData): WebGLTexture {
    const gl = this.contextManager.getContext();

    const texture = this.acquire(
      imageData.width,
      imageData.height,
      gl.RGBA,
      gl.RGBA,
      gl.UNSIGNED_BYTE,
      imageData.data
    );

    return texture;
  }

  /**
   * 创建浮点纹理
   */
  public createFloat32Texture(
    width: number,
    height: number,
    data?: Float32Array
  ): WebGLTexture {
    const gl = this.contextManager.getContext();

    return this.acquire(
      width,
      height,
      gl.RGBA,
      gl.RGBA32F,
      gl.FLOAT,
      data || null
    );
  }

  /**
   * 创建半精度浮点纹理
   */
  public createFloat16Texture(
    width: number,
    height: number,
    data?: Uint16Array
  ): WebGLTexture {
    const gl = this.contextManager.getContext();

    return this.acquire(
      width,
      height,
      gl.RGBA,
      gl.RGBA16F,
      gl.HALF_FLOAT,
      data || null
    );
  }

  /**
   * 创建权重纹理（带缓存）
   */
  public createWeightTexture(name: string, data: Float32Array, shape: number[]): WebGLTexture {
    // 检查缓存
    if (this.weightTextures.has(name)) {
      console.log(`使用缓存的权重纹理: ${name}`);
      return this.weightTextures.get(name)!;
    }

    const gl = this.contextManager.getContext();
    let texture: WebGLTexture;

    try {
      // 根据形状决定纹理类型
      if (shape.length === 4) {
        // 4D 权重（卷积核）: [out_channels, in_channels, height, width]
        const [outChannels, inChannels, height, width] = shape;

        if (height === 1 && width === 1) {
          // 1x1 卷积核，优化为一维纹理
          const textureWidth = Math.min(inChannels || 3, 4); // 最多4个通道
          const textureHeight = 1;
          const expectedSize = textureWidth * textureHeight * 4;

          // 确保数据大小匹配
          const paddedData = new Float32Array(expectedSize);
          for (let i = 0; i < Math.min(data.length, expectedSize); i++) {
            paddedData[i] = data[i] || 0;
          }

          texture = this.createFloat32Texture(textureWidth, textureHeight, paddedData);
          console.log(`创建1x1卷积权重纹理: ${name}, 尺寸 ${textureWidth}x${textureHeight}, 数据大小: ${data.length}->${paddedData.length}`);
        } else {
          // 正常卷积核
          const textureWidth = width || 3;
          const textureHeight = height || 3;
          const expectedSize = textureWidth * textureHeight * 4;

          const paddedData = new Float32Array(expectedSize);
          for (let i = 0; i < Math.min(data.length, expectedSize); i++) {
            paddedData[i] = data[i] || 0;
          }

          texture = this.createFloat32Texture(textureWidth, textureHeight, paddedData);
          console.log(`创建卷积权重纹理: ${name}, 尺寸 ${textureWidth}x${textureHeight}, 数据大小: ${data.length}->${paddedData.length}`);
        }
      } else if (shape.length === 2) {
        // 2D 权重（全连接）: [out_features, in_features]
        const [outFeatures, inFeatures] = shape;
        const textureWidth = inFeatures || 1;
        const textureHeight = outFeatures || 1;
        const expectedSize = textureWidth * textureHeight * 4;

        const paddedData = new Float32Array(expectedSize);
        for (let i = 0; i < Math.min(data.length, expectedSize); i++) {
          paddedData[i] = data[i] || 0;
        }

        texture = this.createFloat32Texture(textureWidth, textureHeight, paddedData);
        console.log(`创建全连接权重纹理: ${name}, 尺寸 ${textureWidth}x${textureHeight}, 数据大小: ${data.length}->${paddedData.length}`);
      } else if (shape.length === 1) {
        // 1D 权重（偏置等）
        const length = shape[0] || 1;

        // 对于小的偏置向量，使用1x1纹理
        if (length <= 4) {
          const paddedData = new Float32Array(4);
          for (let i = 0; i < Math.min(length, 4); i++) {
            paddedData[i] = data[i] || 0;
          }
          texture = this.createFloat32Texture(1, 1, paddedData);
          console.log(`创建小偏置权重纹理: ${name}, 尺寸 1x1, 数据大小: ${data.length}->4`);
        } else {
          // 对于大的偏置向量，使用2D纹理
          const textureWidth = Math.ceil(Math.sqrt(length / 4));
          const textureHeight = Math.ceil(length / (textureWidth * 4));
          const expectedSize = textureWidth * textureHeight * 4;

          const paddedData = new Float32Array(expectedSize);
          for (let i = 0; i < Math.min(data.length, expectedSize); i++) {
            paddedData[i] = data[i] || 0;
          }

          texture = this.createFloat32Texture(textureWidth, textureHeight, paddedData);
          console.log(`创建1D权重纹理: ${name}, 尺寸 ${textureWidth}x${textureHeight}, 数据大小: ${data.length}->${paddedData.length}`);
        }
      } else {
        // 默认情况：创建小纹理
        const paddedData = new Float32Array(4);
        for (let i = 0; i < Math.min(data.length, 4); i++) {
          paddedData[i] = data[i] || 0;
        }
        texture = this.createFloat32Texture(1, 1, paddedData);
        console.log(`创建默认权重纹理: ${name}, 尺寸 1x1, 数据大小: ${data.length}->4`);
      }

      // 缓存纹理
      this.weightTextures.set(name, texture);

      return texture;

    } catch (error) {
      console.error(`创建权重纹理失败: ${name}`, error);
      // 备用方案：创建最小的纹理
      const fallbackData = new Float32Array(4);
      for (let i = 0; i < Math.min(data.length, 4); i++) {
        fallbackData[i] = data[i] || 0;
      }
      texture = this.createFloat32Texture(1, 1, fallbackData);
      this.weightTextures.set(name, texture);
      console.log(`使用备用方案创建权重纹理: ${name}`);
      return texture;
    }
  }

  /**
   * 获取权重纹理
   */
  public getWeightTexture(name: string): WebGLTexture | null {
    return this.weightTextures.get(name) || null;
  }

  /**
   * 清理权重纹理缓存
   */
  public clearWeightCache(): void {
    const gl = this.contextManager.getContext();

    for (const texture of this.weightTextures.values()) {
      // 从正在使用集合中移除
      this.usedTextures.delete(texture);

      // 删除纹理
      gl.deleteTexture(texture);

      // 更新内存使用
      const spec = this.textureSpecs.get(texture);
      if (spec) {
        this.totalMemoryUsage -= spec.size;
        this.textureSpecs.delete(texture);
      }
    }

    this.weightTextures.clear();
    console.log('权重纹理缓存已清理');
  }

  /**
   * 读取纹理数据
   */
  public readTextureData(
    texture: WebGLTexture,
    width: number,
    height: number,
    format: number,
    type: number
  ): ArrayBufferView {
    const gl = this.contextManager.getContext();

    // 创建临时帧缓冲区
    const framebuffer = this.contextManager.createFramebuffer();
    this.contextManager.bindFramebufferTexture(framebuffer, texture);

    // 根据类型创建适当的数组
    let data: ArrayBufferView;
    const pixelCount = width * height * 4; // 假设RGBA

    if (type === gl.UNSIGNED_BYTE) {
      data = new Uint8Array(pixelCount);
    } else if (type === gl.FLOAT) {
      data = new Float32Array(pixelCount);
    } else if (type === gl.HALF_FLOAT) {
      data = new Uint16Array(pixelCount);
    } else {
      throw InferenceError.textureCreationFailed(`Unsupported texture type: ${type}`);
    }

    // 读取像素数据
    gl.readPixels(0, 0, width, height, format, type, data);

    // 清理
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.deleteFramebuffer(framebuffer);

    this.contextManager.checkError('readTextureData');

    return data;
  }

  /**
   * 获取内存使用情况
   */
  public getMemoryUsage(): number {
    return this.totalMemoryUsage;
  }

  /**
   * 获取纹理池大小
   */
  public getPoolSize(): number {
    let totalPooled = 0;
    for (const list of this.availableTextures.values()) {
      totalPooled += list.length;
    }
    return totalPooled;
  }

  /**
   * 获取正在使用的纹理数量
   */
  public getUsedTextureCount(): number {
    return this.usedTextures.size;
  }

  /**
   * 获取纹理信息
   */
  public getTextureInfo(texture: WebGLTexture): TextureSpec | null {
    return this.textureSpecs.get(texture) || null;
  }

  /**
   * 设置最大池大小
   */
  public setMaxPoolSize(size: number): void {
    this.maxPoolSize = Math.max(1, size);
  }

  /**
   * 清理所有纹理
   */
  public cleanup(): void {
    const gl = this.contextManager.getContext();

    // 删除池中的纹理
    for (const textureList of this.availableTextures.values()) {
      for (const texture of textureList) {
        gl.deleteTexture(texture);
      }
    }

    // 删除正在使用的纹理
    for (const texture of this.usedTextures) {
      gl.deleteTexture(texture);
    }

    // 删除权重纹理
    for (const texture of this.weightTextures.values()) {
      gl.deleteTexture(texture);
    }

    // 清空所有集合
    this.availableTextures.clear();
    this.usedTextures.clear();
    this.textureSpecs.clear();
    this.weightTextures.clear();
    this.totalMemoryUsage = 0;
  }

  /**
   * 强制垃圾回收 - 清理长期未使用的纹理
   */
  public forceGarbageCollection(): void {
    const gl = this.contextManager.getContext();

    // 简单策略：清空所有池化纹理
    for (const [specKey, textureList] of this.availableTextures.entries()) {
      for (const texture of textureList) {
        gl.deleteTexture(texture);
        const spec = this.textureSpecs.get(texture);
        if (spec) {
          this.totalMemoryUsage -= spec.size;
          this.textureSpecs.delete(texture);
        }
      }
      textureList.length = 0; // 清空数组
    }
  }
}