import { WebGLExtensions } from '../types/index.js';
import { InferenceError } from '../errors/InferenceError.js';

/**
 * WebGL上下文管理器
 * 负责初始化WebGL上下文、检测扩展支持、管理WebGL资源
 */
export class WebGLContextManager {
  private gl: WebGL2RenderingContext | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private extensions: WebGLExtensions = {};
  private isInitialized = false;
  private debugMode = false; // debug 模式开关

  constructor(debugMode: boolean = false) {
    this.debugMode = debugMode;
  }

  /**
   * 初始化WebGL上下文
   */
  public initialize(canvas: HTMLCanvasElement): boolean {
    try {
      this.canvas = canvas;

      // 尝试获取WebGL2上下文
      const gl = canvas.getContext('webgl2', {
        alpha: false,
        antialias: false,
        depth: false,
        stencil: false,
        preserveDrawingBuffer: true,
        powerPreference: 'high-performance'
      });

      if (!gl) {
        throw InferenceError.webglNotSupported();
      }

      this.gl = gl;

      // 检测必要的扩展
      if (!this.checkExtensions()) {
        throw InferenceError.extensionNotAvailable('Required extensions not available');
      }

      // 配置WebGL状态
      this.configureWebGLState();

      this.isInitialized = true;
      return true;

    } catch (error) {
      console.error('WebGL initialization failed:', error);
      return false;
    }
  }

  /**
   * 检测WebGL扩展支持
   */
  public checkExtensions(): boolean {
    if (!this.gl) {
      return false;
    }

    const requiredExtensions = [
      'EXT_color_buffer_float',
      'OES_texture_float_linear',
      'EXT_float_blend'
    ];

    const optionalExtensions = [
      'WEBGL_color_buffer_float'
    ];

    // 检测必需扩展
    for (const extName of requiredExtensions) {
      const ext = this.gl.getExtension(extName);
      if (!ext) {
        console.error(`Required extension ${extName} not available`);
        return false;
      }
      this.extensions[extName as keyof WebGLExtensions] = ext;
    }

    // 检测可选扩展
    for (const extName of optionalExtensions) {
      const ext = this.gl.getExtension(extName);
      if (ext) {
        this.extensions[extName as keyof WebGLExtensions] = ext;
      }
    }

    return true;
  }

  /**
   * 配置WebGL初始状态
   */
  private configureWebGLState(): void {
    if (!this.gl) return;

    const gl = this.gl;

    // 禁用深度测试
    gl.disable(gl.DEPTH_TEST);
    gl.disable(gl.STENCIL_TEST);
    gl.disable(gl.CULL_FACE);
    gl.disable(gl.BLEND);

    // 设置视口
    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

    // 清空颜色
    gl.clearColor(0.0, 0.0, 0.0, 1.0);
  }

  /**
   * 创建纹理
   */
  public createTexture(
    width: number,
    height: number,
    format: number,
    internalFormat: number,
    type: number,
    data?: ArrayBufferView | null
  ): WebGLTexture {
    if (!this.gl) {
      throw InferenceError.textureCreationFailed('WebGL context not initialized');
    }

    const gl = this.gl;
    const texture = gl.createTexture();

    if (!texture) {
      throw InferenceError.textureCreationFailed('Failed to create WebGL texture');
    }

    gl.bindTexture(gl.TEXTURE_2D, texture);

    // 设置纹理参数
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);

    // 上传纹理数据
    gl.texImage2D(
      gl.TEXTURE_2D,
      0,
      internalFormat,
      width,
      height,
      0,
      format,
      type,
      data || null
    );

    // 只在 debug 模式下检查WebGL错误
    if (this.debugMode) {
      const error = gl.getError();
      if (error !== gl.NO_ERROR) {
        gl.deleteTexture(texture);
        throw InferenceError.textureCreationFailed(`WebGL error: ${error}`);
      }
    }

    gl.bindTexture(gl.TEXTURE_2D, null);
    return texture;
  }

  /**
   * 创建帧缓冲区
   */
  public createFramebuffer(): WebGLFramebuffer {
    if (!this.gl) {
      throw InferenceError.textureCreationFailed('WebGL context not initialized');
    }

    const framebuffer = this.gl.createFramebuffer();
    if (!framebuffer) {
      throw InferenceError.textureCreationFailed('Failed to create framebuffer');
    }

    return framebuffer;
  }

  /**
   * 绑定帧缓冲区和纹理
   */
  public bindFramebufferTexture(framebuffer: WebGLFramebuffer, texture: WebGLTexture): void {
    if (!this.gl) return;

    const gl = this.gl;
    gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer);
    gl.framebufferTexture2D(
      gl.FRAMEBUFFER,
      gl.COLOR_ATTACHMENT0,
      gl.TEXTURE_2D,
      texture,
      0
    );

    // 只在 debug 模式下检查帧缓冲区完整性
    if (this.debugMode) {
      const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);
      if (status !== gl.FRAMEBUFFER_COMPLETE) {
        throw InferenceError.textureCreationFailed(`Framebuffer incomplete: ${status}`);
      }
    }
  }

  /**
   * 获取最大纹理尺寸
   */
  public getMaxTextureSize(): number {
    if (!this.gl) return 0;
    return this.gl.getParameter(this.gl.MAX_TEXTURE_SIZE);
  }

  /**
   * 获取WebGL上下文
   */
  public getContext(): WebGL2RenderingContext {
    if (!this.gl) {
      throw InferenceError.webglNotSupported('WebGL context not initialized');
    }
    return this.gl;
  }

  /**
   * 获取Canvas元素
   */
  public getCanvas(): HTMLCanvasElement {
    if (!this.canvas) {
      throw new Error('Canvas not initialized');
    }
    return this.canvas;
  }

  /**
   * 获取扩展
   */
  public getExtensions(): WebGLExtensions {
    return this.extensions;
  }

  /**
   * 检查是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized && this.gl !== null;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    if (this.gl) {
      // WebGL上下文会在Canvas销毁时自动清理
      this.gl = null;
    }
    this.canvas = null;
    this.extensions = {};
    this.isInitialized = false;
  }

  /**
   * 检查WebGL错误
   */
  public checkError(operation?: string): void {
    // 只在 debug 模式下执行错误检查
    if (!this.debugMode || !this.gl) return;

    const error = this.gl.getError();
    if (error !== this.gl.NO_ERROR) {
      const errorName = this.getErrorName(error);
      const message = operation
        ? `WebGL error in ${operation}: ${errorName}`
        : `WebGL error: ${errorName}`;
      throw InferenceError.inferenceExecutionError(message, { errorCode: error });
    }
  }

  /**
   * 获取错误名称
   */
  private getErrorName(error: number): string {
    if (!this.gl) return 'UNKNOWN';

    const gl = this.gl;
    switch (error) {
      case gl.NO_ERROR:
        return 'NO_ERROR';
      case gl.INVALID_ENUM:
        return 'INVALID_ENUM';
      case gl.INVALID_VALUE:
        return 'INVALID_VALUE';
      case gl.INVALID_OPERATION:
        return 'INVALID_OPERATION';
      case gl.INVALID_FRAMEBUFFER_OPERATION:
        return 'INVALID_FRAMEBUFFER_OPERATION';
      case gl.OUT_OF_MEMORY:
        return 'OUT_OF_MEMORY';
      case gl.CONTEXT_LOST_WEBGL:
        return 'CONTEXT_LOST_WEBGL';
      default:
        return `UNKNOWN_ERROR_${error}`;
    }
  }

  /**
   * 检查是否处于debug模式
   */
  public isDebugMode(): boolean {
    return this.debugMode;
  }
}

// 导出接口
export interface IWebGLContextManager {
  initialize(canvas: HTMLCanvasElement): boolean;
  isReady(): boolean;
  getContext(): WebGL2RenderingContext;
  createTexture(
    width: number,
    height: number,
    format: number,
    internalFormat: number,
    type: number,
    data?: ArrayBufferView | null
  ): WebGLTexture;
  createFramebuffer(): WebGLFramebuffer;
  bindFramebufferTexture(framebuffer: WebGLFramebuffer, texture: WebGLTexture): void;
  checkError(operation?: string): void;
  getMaxTextureSize(): number;
  isDebugMode(): boolean;
  dispose(): void;
}
