#!/usr/bin/env python3
"""
生成简单的ONNX测试模型
这个脚本创建一个简单的灰度化模型用于WebGL ONNX推理库测试
"""

import numpy as np
import onnx
from onnx import helper, TensorProto, ValueInfoProto

def create_grayscale_model():
    """
    创建一个简单的RGB到灰度转换模型
    使用标准灰度化公式: Gray = 0.299*R + 0.587*G + 0.114*B
    """
    
    # 定义输入
    # 输入形状: [1, 3, H, W] (NCHW格式)
    input_tensor = helper.make_tensor_value_info(
        'input', TensorProto.FLOAT, [1, 3, 224, 224]
    )
    
    # 定义输出
    # 输出形状: [1, 1, H, W] (灰度图)
    output_tensor = helper.make_tensor_value_info(
        'output', TensorProto.FLOAT, [1, 1, 224, 224]
    )
    
    # 创建权重常量
    # RGB到灰度的权重 [0.299, 0.587, 0.114]
    weights_data = np.array([0.299, 0.587, 0.114], dtype=np.float32).reshape(1, 3, 1, 1)
    weights_tensor = helper.make_tensor(
        'weights',
        TensorProto.FLOAT,
        weights_data.shape,
        weights_data.flatten()
    )
    
    # 创建卷积节点
    conv_node = helper.make_node(
        'Conv',                    # 操作类型
        inputs=['input', 'weights'], # 输入
        outputs=['output'],        # 输出
        name='grayscale_conv',     # 节点名称
        kernel_shape=[1, 1],       # 1x1卷积核
        strides=[1, 1],           # 步长
        pads=[0, 0, 0, 0],        # 填充
        group=1                   # 分组
    )
    
    # 创建计算图
    graph_def = helper.make_graph(
        nodes=[conv_node],
        name='GrayscaleModel',
        inputs=[input_tensor],
        outputs=[output_tensor],
        initializer=[weights_tensor]
    )
    
    # 创建模型
    model_def = helper.make_model(
        graph_def,
        producer_name='WebGL-ONNX-Test',
        producer_version='1.0'
    )
    
    # 设置版本信息
    model_def.opset_import[0].version = 11
    
    # 验证模型
    onnx.checker.check_model(model_def)
    
    return model_def

def create_simple_add_model():
    """
    创建一个简单的加法模型用于测试
    """
    
    # 定义输入
    input1 = helper.make_tensor_value_info(
        'input1', TensorProto.FLOAT, [1, 1, 4, 4]
    )
    input2 = helper.make_tensor_value_info(
        'input2', TensorProto.FLOAT, [1, 1, 4, 4]
    )
    
    # 定义输出
    output = helper.make_tensor_value_info(
        'output', TensorProto.FLOAT, [1, 1, 4, 4]
    )
    
    # 创建常量输入
    const_data = np.ones((1, 1, 4, 4), dtype=np.float32) * 0.5
    const_tensor = helper.make_tensor(
        'const_input',
        TensorProto.FLOAT,
        const_data.shape,
        const_data.flatten()
    )
    
    # 创建加法节点
    add_node = helper.make_node(
        'Add',
        inputs=['input1', 'const_input'],
        outputs=['output'],
        name='simple_add'
    )
    
    # 创建计算图
    graph_def = helper.make_graph(
        nodes=[add_node],
        name='SimpleAddModel',
        inputs=[input1],
        outputs=[output],
        initializer=[const_tensor]
    )
    
    # 创建模型
    model_def = helper.make_model(
        graph_def,
        producer_name='WebGL-ONNX-Test',
        producer_version='1.0'
    )
    
    # 设置版本信息
    model_def.opset_import[0].version = 11
    
    # 验证模型
    onnx.checker.check_model(model_def)
    
    return model_def

if __name__ == '__main__':
    print("正在生成测试ONNX模型...")
    
    # 生成灰度化模型
    grayscale_model = create_grayscale_model()
    with open('public/model.onnx', 'wb') as f:
        f.write(grayscale_model.SerializeToString())
    print("✅ 灰度化模型已保存到 public/model.onnx")
    
    # 生成简单加法模型
    add_model = create_simple_add_model()
    with open('public/simple_add.onnx', 'wb') as f:
        f.write(add_model.SerializeToString())
    print("✅ 简单加法模型已保存到 public/simple_add.onnx")
    
    # 显示模型信息
    print("\n模型信息:")
    print(f"灰度化模型: {len(grayscale_model.SerializeToString())} 字节")
    print(f"加法模型: {len(add_model.SerializeToString())} 字节")
    
    print("\n模型生成完成！")